#include <rclcpp/rclcpp.hpp>
#include "geometry_msgs/msg/pose_stamped.hpp"
#include "data_interface/srv/pose_est3d2d_srv.hpp"
#include "data_interface/msg/pose_est3d2d_points_msg.hpp"

using namespace std::chrono_literals;

class PoseEstimationClient : public rclcpp::Node {
public:
    PoseEstimationClient() : Node("pose_estimation_client") {
        // 创建客户端
        client_ = this->create_client<data_interface::srv::PoseEst3d2dSrv>("pose_estimation_3d2d");
    }

    bool send_request(const data_interface::msg::PoseEst3d2dPointsMsg & points) {
        const int max_attempts = 5;  // 最大尝试次数
        int attempts = 0;           // 当前尝试次数
        
        // 确保服务可用（有限次尝试）
        while (attempts < max_attempts && !client_->wait_for_service(1s)) {
            RCLCPP_WARN(get_logger(), "Service not available, waiting... (%d/%d)", 
                         attempts + 1, max_attempts);
            attempts++;
        }

        // 检查是否超过最大尝试次数
        if (attempts >= max_attempts) {
            RCLCPP_ERROR(get_logger(), "Service unavailable after %d attempts. Exiting.", max_attempts);
            return false;
        }

        // 创建服务请求
        auto request = std::make_shared<data_interface::srv::PoseEst3d2dSrv::Request>();
        request->points = points;  // 使用服务请求中定义的实际字段名
        
        // 发送异步请求
        auto future_and_request_id = client_->async_send_request(request);
        
        // 显式获取共享 future
        future_ = future_and_request_id.future.share();
        return true;
    }

    // 添加获取future的方法
    rclcpp::Client<data_interface::srv::PoseEst3d2dSrv>::SharedFuture get_future() {
        return future_;
    }

private:
    rclcpp::Client<data_interface::srv::PoseEst3d2dSrv>::SharedPtr client_;
    rclcpp::Client<data_interface::srv::PoseEst3d2dSrv>::SharedFuture future_;
};

int main(int argc, char * argv[]) {
    rclcpp::init(argc, argv);
    
    // 创建客户端节点
    auto client_node = std::make_shared<PoseEstimationClient>();
    
    // 创建消息对象
    data_interface::msg::PoseEst3d2dPointsMsg points_msg;
    
    // 根据实际消息定义调整赋值
    // 假设消息中有一个 points 数组
    const double data[5][2] = {
        {1148, 301},
        {1185, 301},
        {1221, 301},
        {1166, 331},
        {1201, 331}
    };

    // 填充消息数组
    for (int i = 0; i < 5; ++i) {
        points_msg.points[i].x = data[i][0];
        points_msg.points[i].y = data[i][1];
        points_msg.points[i].label = i;  // 设置标签
    }
    
    // 发送请求（检查是否成功）
    if (!client_node->send_request(points_msg)) {
        rclcpp::shutdown();
        return 1;  // 服务不可用，直接退出
    }
    
    // 获取future
    auto future = client_node->get_future();

    // 等待响应
    if (rclcpp::spin_until_future_complete(client_node, future) != rclcpp::FutureReturnCode::SUCCESS) {
        RCLCPP_ERROR(client_node->get_logger(), "Service call failed");
        rclcpp::shutdown();
        return 1;
    }
    
    // 处理响应
    auto response = future.get();
    auto pose = response->pose;
    
    RCLCPP_INFO(client_node->get_logger(), "Received pose: x=%.2f, y=%.2f, z=%.2f",
                pose.pose.position.x, 
                pose.pose.position.y,
                pose.pose.position.z);
    
    RCLCPP_INFO(client_node->get_logger(), "Orientation: x=%.2f, y=%.2f, z=%.2f, w=%.2f",
                pose.pose.orientation.x,
                pose.pose.orientation.y,
                pose.pose.orientation.z,
                pose.pose.orientation.w);
    
    rclcpp::shutdown();
    return 0;
}