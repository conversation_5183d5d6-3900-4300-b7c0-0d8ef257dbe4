#pragma once

#include <chrono>
#include <rclcpp/rclcpp.hpp>

/**
 * @brief 服务请求管理器，提供多种请求控制策略
 */
class ServiceRequestManager {
public:
    enum class RequestMode {
        ONCE_ONLY,          // 只请求一次
        COOLDOWN,           // 冷却时间控制
        CONDITION_BASED,    // 基于条件控制
        UNLIMITED           // 无限制
    };

    ServiceRequestManager(RequestMode mode = RequestMode::ONCE_ONLY, 
                         std::chrono::milliseconds cooldown = std::chrono::milliseconds(2000))
        : mode_(mode), cooldown_duration_(cooldown) {
        reset();
    }

    /**
     * @brief 检查是否可以发送请求
     */
    bool can_send_request() {
        auto now = std::chrono::steady_clock::now();
        
        switch (mode_) {
            case RequestMode::ONCE_ONLY:
                return !request_completed_ && !request_pending_;
                
            case RequestMode::COOLDOWN:
                return !request_pending_ && 
                       (now - last_request_time_) >= cooldown_duration_;
                       
            case RequestMode::CONDITION_BASED:
                return !request_pending_ && should_request_condition_;
                
            case RequestMode::UNLIMITED:
                return !request_pending_;
                
            default:
                return false;
        }
    }

    /**
     * @brief 标记请求开始
     */
    void mark_request_started() {
        request_pending_ = true;
        last_request_time_ = std::chrono::steady_clock::now();
        request_count_++;
    }

    /**
     * @brief 标记请求完成
     */
    void mark_request_completed(bool success = true) {
        request_pending_ = false;
        if (success) {
            request_completed_ = true;
            successful_requests_++;
        } else {
            failed_requests_++;
        }
    }

    /**
     * @brief 重置状态
     */
    void reset() {
        request_pending_ = false;
        request_completed_ = false;
        should_request_condition_ = true;
        request_count_ = 0;
        successful_requests_ = 0;
        failed_requests_ = 0;
        last_request_time_ = std::chrono::steady_clock::time_point{};
    }

    /**
     * @brief 设置条件（用于CONDITION_BASED模式）
     */
    void set_condition(bool condition) {
        should_request_condition_ = condition;
    }

    /**
     * @brief 获取统计信息
     */
    struct Stats {
        int total_requests;
        int successful_requests;
        int failed_requests;
        bool is_completed;
        bool is_pending;
    };

    Stats get_stats() const {
        return {
            request_count_,
            successful_requests_,
            failed_requests_,
            request_completed_,
            request_pending_
        };
    }

private:
    RequestMode mode_;
    std::chrono::milliseconds cooldown_duration_;
    
    bool request_pending_ = false;
    bool request_completed_ = false;
    bool should_request_condition_ = true;
    
    int request_count_ = 0;
    int successful_requests_ = 0;
    int failed_requests_ = 0;
    
    std::chrono::steady_clock::time_point last_request_time_;
};
