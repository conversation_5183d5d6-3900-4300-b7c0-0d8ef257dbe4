# 服务请求控制使用说明

## 概述
该系统实现了服务请求的智能控制，确保服务只请求一次，并提供多种重新请求的方式。

## 功能特性

### 1. 自动请求控制
- **一次性请求**: 服务请求成功后自动标记为完成，不会重复请求
- **冷却时间**: 防止频繁请求（默认2秒冷却时间）
- **状态跟踪**: 实时跟踪请求状态（准备/进行中/已完成）

### 2. 重新请求方式

#### 方式1: 通过ROS服务重置
```bash
# 重置请求状态，允许新的请求
ros2 service call /reset_pose_request std_srvs/srv/Empty

# 查询当前请求状态
ros2 service call /get_request_status std_srvs/srv/Trigger
```

#### 方式2: 通过键盘快捷键
在OpenCV窗口激活时：
- 按 `R` 键：重置请求状态
- 按 `S` 键：显示当前状态

#### 方式3: 程序内部调用
```cpp
// 检查是否可以重新请求
if (node->can_request_again()) {
    node->reset_service_request_state();
}

// 或者强制重置
node->force_new_request();
```

## 状态说明

### 请求状态
- **READY**: 准备就绪，可以发送新请求
- **PENDING**: 请求正在处理中
- **COMPLETED**: 请求已完成，需要重置才能发送新请求

### 状态转换
```
READY → PENDING → COMPLETED
   ↑                 ↓
   ←── reset() ←────
```

## 使用示例

### 1. 启动节点
```bash
ros2 run detected_post_processing post_processing
```

### 2. 监控状态
```bash
# 持续监控状态
watch -n 1 'ros2 service call /get_request_status std_srvs/srv/Trigger'
```

### 3. 重置请求
```bash
# 当需要重新请求时
ros2 service call /reset_pose_request std_srvs/srv/Empty
```

## 配置选项

### 修改冷却时间
在代码中修改：
```cpp
static constexpr std::chrono::seconds REQUEST_COOLDOWN{5};  // 改为5秒
```

### 禁用一次性限制
注释掉完成标记：
```cpp
// service_request_completed_ = true;  // 注释这行
```

## 日志信息

### 正常流程
```
[INFO] Service request sent successfully
[INFO] Received pose: x=0.60, y=-0.04, z=0.38
[INFO] Service request completed successfully
```

### 重复请求被阻止
```
[DEBUG] Service request already completed, skipping...
```

### 重置状态
```
[INFO] Service request state reset - ready for new requests
```

## 故障排除

### 问题1: 无法发送新请求
**症状**: 日志显示 "Service request already completed, skipping..."
**解决**: 调用重置服务或按R键重置状态

### 问题2: 请求一直处于PENDING状态
**症状**: 状态查询显示 "Request is currently pending"
**解决**: 检查服务端是否正常响应，必要时重启节点

### 问题3: 服务调用失败
**症状**: "Service not available" 错误
**解决**: 确保pose_estimation_3d2d服务正在运行

## API参考

### 公共方法
```cpp
bool send_request_async(const data_interface::msg::PoseEst3d2dPointsMsg & points);
void reset_service_request_state();
bool can_request_again();
void force_new_request();
```

### 服务接口
- `/reset_pose_request` (std_srvs/srv/Empty): 重置请求状态
- `/get_request_status` (std_srvs/srv/Trigger): 获取当前状态

### 键盘快捷键
- `R`: 重置请求状态
- `S`: 显示当前状态
