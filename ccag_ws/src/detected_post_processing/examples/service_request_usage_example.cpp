// 使用示例：如何在你的代码中集成服务请求管理器

#include "../include/service_request_manager.hpp"

class DetectedPostProcessingWithManager : public rclcpp::Node {
public:
    DetectedPostProcessingWithManager() : Node("post_processing_with_manager") {
        // 初始化服务请求管理器
        // 方式1：只请求一次
        request_manager_ = std::make_unique<ServiceRequestManager>(
            ServiceRequestManager::RequestMode::ONCE_ONLY);
            
        // 方式2：冷却时间控制（每2秒最多一次）
        // request_manager_ = std::make_unique<ServiceRequestManager>(
        //     ServiceRequestManager::RequestMode::COOLDOWN, 
        //     std::chrono::milliseconds(2000));
            
        // 方式3：基于条件控制
        // request_manager_ = std::make_unique<ServiceRequestManager>(
        //     ServiceRequestManager::RequestMode::CONDITION_BASED);
        
        client_ = this->create_client<data_interface::srv::PoseEst3d2dSrv>("pose_estimation_3d2d");
    }

private:
    // 修改后的发送请求函数
    bool send_request_async_managed(const data_interface::msg::PoseEst3d2dPointsMsg & points) {
        // 检查是否可以发送请求
        if (!request_manager_->can_send_request()) {
            auto stats = request_manager_->get_stats();
            RCLCPP_DEBUG(get_logger(), 
                "Request blocked - Total: %d, Success: %d, Failed: %d, Completed: %s, Pending: %s",
                stats.total_requests, stats.successful_requests, stats.failed_requests,
                stats.is_completed ? "true" : "false", stats.is_pending ? "true" : "false");
            return false;
        }
        
        // 服务可用性检查
        if (!client_->wait_for_service(std::chrono::seconds(1))) {
            RCLCPP_ERROR(get_logger(), "Service not available");
            return false;
        }

        // 创建服务请求
        auto request = std::make_shared<data_interface::srv::PoseEst3d2dSrv::Request>();
        request->points = points;
        
        // 标记请求开始
        request_manager_->mark_request_started();
        
        // 发送异步请求
        auto future = client_->async_send_request(request, 
            std::bind(&DetectedPostProcessingWithManager::service_response_callback_managed, 
                     this, std::placeholders::_1));
        
        RCLCPP_INFO(get_logger(), "Service request sent successfully");
        return true;
    }

    // 修改后的响应回调函数
    void service_response_callback_managed(
        rclcpp::Client<data_interface::srv::PoseEst3d2dSrv>::SharedFuture future) {
        
        bool success = false;
        try {
            auto response = future.get();
            auto pose = response->pose;
            
            RCLCPP_INFO(this->get_logger(), "Received pose: x=%.2f, y=%.2f, z=%.2f",
                        pose.pose.position.x, 
                        pose.pose.position.y,
                        pose.pose.position.z);
            
            // 发布处理后的位姿信息
            pub_charge_pose_->publish(pose);
            success = true;
            
        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "Service call exception: %s", e.what());
            success = false;
        }
        
        // 标记请求完成
        request_manager_->mark_request_completed(success);
        
        // 打印统计信息
        auto stats = request_manager_->get_stats();
        RCLCPP_INFO(get_logger(), 
            "Request completed - Total: %d, Success: %d, Failed: %d",
            stats.total_requests, stats.successful_requests, stats.failed_requests);
    }

    // 重置请求状态的公共接口
    void reset_request_manager() {
        request_manager_->reset();
        RCLCPP_INFO(get_logger(), "Request manager reset");
    }

    // 设置条件（用于CONDITION_BASED模式）
    void set_request_condition(bool condition) {
        request_manager_->set_condition(condition);
    }

private:
    std::unique_ptr<ServiceRequestManager> request_manager_;
    rclcpp::Client<data_interface::srv::PoseEst3d2dSrv>::SharedPtr client_;
    rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr pub_charge_pose_;
};

// 在sync_callback中的使用示例：
void sync_callback_example(const sensor_msgs::msg::Image::SharedPtr msg_image, 
                          const vision_msgs::msg::BoundingBox2DArray::SharedPtr msg_bbox) {
    // ... 图像处理逻辑 ...
    
    if (circles.size() == 5) {
        // 准备请求数据
        data_interface::msg::PoseEst3d2dPointsMsg points_msg;
        for (size_t i = 0; i < image_points.size(); ++i) {
            points_msg.points[i].x = image_points[i].x;
            points_msg.points[i].y = image_points[i].y;
            points_msg.points[i].label = i;
        }
        
        // 发送管理的异步请求
        send_request_async_managed(points_msg);
        // 注意：这里不会阻塞，响应将在回调中处理
    }
}
