/**
 * @file pose_transform_examples.cpp
 * @brief 位姿和变换矩阵转换的使用示例
 */

#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/pose.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <tf2/LinearMath/Quaternion.h>
#include <tf2/LinearMath/Matrix3x3.h>
#include <opencv2/opencv.hpp>
#include <cmath>

class PoseTransformExamples {
public:
    /**
     * @brief 将 geometry_msgs::msg::Pose 转换为 4x4 变换矩阵
     */
    static cv::Mat pose_to_transform_matrix(const geometry_msgs::msg::Pose& pose) {
        cv::Mat transform_matrix = cv::Mat::eye(4, 4, CV_64F);
        
        // 位置
        transform_matrix.at<double>(0, 3) = pose.position.x;
        transform_matrix.at<double>(1, 3) = pose.position.y;
        transform_matrix.at<double>(2, 3) = pose.position.z;
        
        // 四元数转旋转矩阵
        tf2::Quaternion q(pose.orientation.x, pose.orientation.y, 
                         pose.orientation.z, pose.orientation.w);
        tf2::Matrix3x3 rot_matrix(q);
        
        for (int i = 0; i < 3; i++) {
            for (int j = 0; j < 3; j++) {
                transform_matrix.at<double>(i, j) = rot_matrix[i][j];
            }
        }
        
        return transform_matrix;
    }

    /**
     * @brief 将 4x4 变换矩阵转换为 geometry_msgs::msg::Pose
     */
    static geometry_msgs::msg::Pose transform_matrix_to_pose(const cv::Mat& transform_matrix) {
        geometry_msgs::msg::Pose pose;
        
        // 位置
        pose.position.x = transform_matrix.at<double>(0, 3);
        pose.position.y = transform_matrix.at<double>(1, 3);
        pose.position.z = transform_matrix.at<double>(2, 3);
        
        // 旋转矩阵转四元数
        tf2::Matrix3x3 rot_matrix(
            transform_matrix.at<double>(0, 0), transform_matrix.at<double>(0, 1), transform_matrix.at<double>(0, 2),
            transform_matrix.at<double>(1, 0), transform_matrix.at<double>(1, 1), transform_matrix.at<double>(1, 2),
            transform_matrix.at<double>(2, 0), transform_matrix.at<double>(2, 1), transform_matrix.at<double>(2, 2)
        );
        
        tf2::Quaternion q;
        rot_matrix.getRotation(q);
        
        pose.orientation.x = q.x();
        pose.orientation.y = q.y();
        pose.orientation.z = q.z();
        pose.orientation.w = q.w();
        
        return pose;
    }

    /**
     * @brief 矩阵乘法示例：组合两个变换
     */
    static cv::Mat compose_transforms(const cv::Mat& T1, const cv::Mat& T2) {
        return T1 * T2;
    }

    /**
     * @brief 计算变换的逆
     */
    static cv::Mat inverse_transform(const cv::Mat& transform) {
        cv::Mat inv_transform = cv::Mat::eye(4, 4, CV_64F);
        
        // 旋转部分的转置
        cv::Mat R = transform(cv::Rect(0, 0, 3, 3));
        cv::Mat R_T = R.t();
        R_T.copyTo(inv_transform(cv::Rect(0, 0, 3, 3)));
        
        // 平移部分的变换
        cv::Mat t = transform(cv::Rect(3, 0, 1, 3));
        cv::Mat inv_t = -R_T * t;
        inv_t.copyTo(inv_transform(cv::Rect(3, 0, 1, 3)));
        
        return inv_transform;
    }

    /**
     * @brief 打印变换矩阵
     */
    static void print_transform_matrix(const cv::Mat& transform, const std::string& name = "Transform") {
        std::cout << name << " Matrix:" << std::endl;
        for (int i = 0; i < 4; i++) {
            std::cout << "  [";
            for (int j = 0; j < 4; j++) {
                std::cout << std::fixed << std::setprecision(4) << transform.at<double>(i, j);
                if (j < 3) std::cout << ", ";
            }
            std::cout << "]" << std::endl;
        }
        std::cout << std::endl;
    }

    /**
     * @brief 打印位姿信息
     */
    static void print_pose(const geometry_msgs::msg::Pose& pose, const std::string& name = "Pose") {
        std::cout << name << ":" << std::endl;
        std::cout << "  Position: x=" << pose.position.x 
                  << ", y=" << pose.position.y 
                  << ", z=" << pose.position.z << std::endl;
        std::cout << "  Orientation: x=" << pose.orientation.x 
                  << ", y=" << pose.orientation.y 
                  << ", z=" << pose.orientation.z 
                  << ", w=" << pose.orientation.w << std::endl;
        std::cout << std::endl;
    }

    /**
     * @brief 创建示例位姿
     */
    static geometry_msgs::msg::Pose create_example_pose(double x, double y, double z, 
                                                        double roll, double pitch, double yaw) {
        geometry_msgs::msg::Pose pose;
        
        pose.position.x = x;
        pose.position.y = y;
        pose.position.z = z;
        
        tf2::Quaternion q;
        q.setRPY(roll, pitch, yaw);
        
        pose.orientation.x = q.x();
        pose.orientation.y = q.y();
        pose.orientation.z = q.z();
        pose.orientation.w = q.w();
        
        return pose;
    }

    /**
     * @brief 运行所有示例
     */
    static void run_examples() {
        std::cout << "=== 位姿和变换矩阵转换示例 ===" << std::endl << std::endl;

        // 示例1：基本转换
        std::cout << "示例1：基本位姿到变换矩阵转换" << std::endl;
        auto pose1 = create_example_pose(1.0, 2.0, 3.0, 0, 0, M_PI/4);  // 绕Z轴旋转45度
        print_pose(pose1, "原始位姿");
        
        auto transform1 = pose_to_transform_matrix(pose1);
        print_transform_matrix(transform1, "变换矩阵");
        
        auto recovered_pose1 = transform_matrix_to_pose(transform1);
        print_pose(recovered_pose1, "恢复的位姿");

        // 示例2：变换组合
        std::cout << "示例2：变换组合" << std::endl;
        auto pose2 = create_example_pose(0.5, 0, 0, 0, 0, M_PI/2);  // 绕Z轴旋转90度
        auto transform2 = pose_to_transform_matrix(pose2);
        
        auto combined_transform = compose_transforms(transform1, transform2);
        print_transform_matrix(combined_transform, "组合变换");
        
        auto combined_pose = transform_matrix_to_pose(combined_transform);
        print_pose(combined_pose, "组合位姿");

        // 示例3：逆变换
        std::cout << "示例3：逆变换" << std::endl;
        auto inv_transform = inverse_transform(transform1);
        print_transform_matrix(inv_transform, "逆变换矩阵");
        
        // 验证：T * T^-1 = I
        auto identity_check = compose_transforms(transform1, inv_transform);
        print_transform_matrix(identity_check, "验证单位矩阵 (T * T^-1)");

        // 示例4：坐标变换
        std::cout << "示例4：点的坐标变换" << std::endl;
        cv::Mat point = (cv::Mat_<double>(4, 1) << 1.0, 0.0, 0.0, 1.0);  // 齐次坐标
        cv::Mat transformed_point = transform1 * point;
        
        std::cout << "原始点: [" << point.at<double>(0, 0) << ", " 
                  << point.at<double>(1, 0) << ", " << point.at<double>(2, 0) << "]" << std::endl;
        std::cout << "变换后: [" << transformed_point.at<double>(0, 0) << ", " 
                  << transformed_point.at<double>(1, 0) << ", " 
                  << transformed_point.at<double>(2, 0) << "]" << std::endl;
    }
};

int main() {
    PoseTransformExamples::run_examples();
    return 0;
}
