cmake_minimum_required(VERSION 3.8)
project(pose_transform_examples)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(tf2 REQUIRED)
find_package(tf2_geometry_msgs REQUIRED)
find_package(OpenCV REQUIRED)

# Add executable
add_executable(pose_transform_examples pose_transform_examples.cpp)

# Include directories
target_include_directories(pose_transform_examples PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>
  ${OpenCV_INCLUDE_DIRS}
)

# Link libraries
target_link_libraries(pose_transform_examples 
  ${OpenCV_LIBS}
)

# Dependencies
ament_target_dependencies(pose_transform_examples
  rclcpp
  geometry_msgs
  tf2
  tf2_geometry_msgs
)

# Install
install(TARGETS pose_transform_examples
  DESTINATION lib/${PROJECT_NAME}
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
