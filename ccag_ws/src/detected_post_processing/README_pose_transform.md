# 位姿与变换矩阵转换工具

## 概述

本工具提供了在ROS2中将`geometry_msgs::msg::Pose`与4x4变换矩阵（OpenCV的`cv::Mat`）之间进行转换的功能。

## 功能特性

- **位姿到变换矩阵**: 将ROS2位姿消息转换为4x4变换矩阵
- **变换矩阵到位姿**: 将4x4变换矩阵转换为ROS2位姿消息
- **矩阵组合**: 组合多个变换矩阵
- **逆变换**: 计算变换矩阵的逆
- **坐标变换**: 使用变换矩阵转换点的坐标

## 核心函数

### 1. 位姿到变换矩阵

```cpp
/**
 * @brief 将 geometry_msgs::msg::Pose 转换为 4x4 变换矩阵
 * 
 * @param pose 输入的位姿消息
 * @return cv::Mat 4x4 变换矩阵
 */
cv::Mat pose_to_transform_matrix(const geometry_msgs::msg::Pose& pose);
```

### 2. 变换矩阵到位姿

```cpp
/**
 * @brief 将变换矩阵转换为 Pose
 * 
 * @param transform_matrix 4x4 变换矩阵
 * @return geometry_msgs::msg::Pose 位姿消息
 */
geometry_msgs::msg::Pose transform_matrix_to_pose(const cv::Mat& transform_matrix);
```

### 3. PoseStamped到变换矩阵

```cpp
/**
 * @brief 将 PoseStamped 转换为 4x4 变换矩阵
 * 
 * @param pose_stamped 输入的带时间戳位姿消息
 * @return cv::Mat 4x4 变换矩阵
 */
cv::Mat pose_stamped_to_transform_matrix(const geometry_msgs::msg::PoseStamped& pose_stamped);
```

## 使用示例

### 基本转换

```cpp
// 创建一个位姿
geometry_msgs::msg::Pose pose;
pose.position.x = 1.0;
pose.position.y = 2.0;
pose.position.z = 3.0;

// 设置四元数（绕Z轴旋转45度）
tf2::Quaternion q;
q.setRPY(0, 0, M_PI/4);
pose.orientation.x = q.x();
pose.orientation.y = q.y();
pose.orientation.z = q.z();
pose.orientation.w = q.w();

// 转换为变换矩阵
cv::Mat transform = pose_to_transform_matrix(pose);

// 打印变换矩阵
std::cout << "Transform matrix:" << std::endl;
for (int i = 0; i < 4; i++) {
    std::cout << "  [" << transform.at<double>(i, 0) << ", "
              << transform.at<double>(i, 1) << ", "
              << transform.at<double>(i, 2) << ", "
              << transform.at<double>(i, 3) << "]" << std::endl;
}

// 转换回位姿
geometry_msgs::msg::Pose recovered_pose = transform_matrix_to_pose(transform);
```

### 组合变换

```cpp
// 创建两个变换矩阵
cv::Mat T1 = pose_to_transform_matrix(pose1);
cv::Mat T2 = pose_to_transform_matrix(pose2);

// 组合变换（T1 * T2）
cv::Mat combined_transform = T1 * T2;

// 转换回位姿
geometry_msgs::msg::Pose combined_pose = transform_matrix_to_pose(combined_transform);
```

### 计算逆变换

```cpp
// 创建变换矩阵
cv::Mat T = pose_to_transform_matrix(pose);

// 计算逆变换
cv::Mat T_inv = cv::Mat::eye(4, 4, CV_64F);
cv::Mat R = T(cv::Rect(0, 0, 3, 3));
cv::Mat R_T = R.t();
R_T.copyTo(T_inv(cv::Rect(0, 0, 3, 3)));

cv::Mat t = T(cv::Rect(3, 0, 1, 3));
cv::Mat inv_t = -R_T * t;
inv_t.copyTo(T_inv(cv::Rect(3, 0, 1, 3)));

// 验证：T * T_inv 应该接近单位矩阵
cv::Mat I = T * T_inv;
```

### 坐标变换

```cpp
// 创建一个点（齐次坐标）
cv::Mat point = (cv::Mat_<double>(4, 1) << 1.0, 0.0, 0.0, 1.0);

// 应用变换
cv::Mat transformed_point = transform * point;

// 提取结果
double x = transformed_point.at<double>(0, 0);
double y = transformed_point.at<double>(1, 0);
double z = transformed_point.at<double>(2, 0);
```

## 数学原理

### 变换矩阵结构

4x4变换矩阵的结构如下：

```
| R11 R12 R13 Tx |
| R21 R22 R23 Ty |
| R31 R32 R33 Tz |
|  0   0   0   1 |
```

其中：
- R是3x3旋转矩阵
- T是3x1平移向量

### 四元数到旋转矩阵

四元数q = [x, y, z, w]转换为旋转矩阵R的公式：

```
R = | 1-2y²-2z²   2xy-2wz    2xz+2wy  |
    | 2xy+2wz     1-2x²-2z²  2yz-2wx  |
    | 2xz-2wy     2yz+2wx    1-2x²-2y²|
```

## 注意事项

1. 确保四元数已经归一化
2. 变换矩阵必须是4x4的，类型为CV_64F
3. 组合变换时，顺序很重要：T1 * T2 ≠ T2 * T1

## 完整示例

请参考 `examples/pose_transform_examples.cpp` 获取完整的使用示例。

## 编译和运行示例

```bash
# 进入示例目录
cd ccag_ws/src/detected_post_processing/examples

# 编译
cmake .
make

# 运行
./pose_transform_examples
```
