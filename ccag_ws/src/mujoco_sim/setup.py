from setuptools import find_packages, setup
import os
from glob import glob

package_name = 'mujoco_sim'

setup(
    name=package_name,
    version='0.0.0',
    packages=find_packages(exclude=['test']),
    data_files=[
        ('share/ament_index/resource_index/packages', ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        (os.path.join('share', package_name, 'launch'), glob(os.path.join('launch', '*launch.[pxy][yma]*'))),
        # 包含 panda_description 根目录下的文件（过滤掉子目录）
        # (os.path.join('share', package_name, 'descriptions', 'panda_description'),
        #  [f for f in glob(os.path.join('mujoco_sim', 'descriptions', 'panda_description', '*')) if os.path.isfile(f)]),
        # 包含 meshes/collision 子目录的所有文件
        (os.path.join('share', package_name, 'descriptions', 'panda_description', 'meshes', 'collision'),
         glob(os.path.join('mujoco_sim', 'descriptions', 'panda_description', 'meshes', 'collision', '*.*'))),
        # 包含 meshes/visual 子目录的所有文件
        (os.path.join('share', package_name, 'descriptions', 'panda_description', 'meshes', 'visual'),
         glob(os.path.join('mujoco_sim', 'descriptions', 'panda_description', 'meshes', 'visual', '*.*'))),
        # 包含 mjcf 子目录的所有文件
        (os.path.join('share', package_name, 'descriptions', 'panda_description', 'mjcf'),
         glob(os.path.join('mujoco_sim', 'descriptions', 'panda_description', 'mjcf', '*.*'))),
        # 包含 srdf 子目录的所有文件
        (os.path.join('share', package_name, 'descriptions', 'panda_description', 'srdf'),
         glob(os.path.join('mujoco_sim', 'descriptions', 'panda_description', 'srdf', '*.*'))),
        # 包含 urdf 子目录的所有文件
        (os.path.join('share', package_name, 'descriptions', 'panda_description', 'urdf'),
         glob(os.path.join('mujoco_sim', 'descriptions', 'panda_description', 'urdf', '*.*'))),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='simon',
    maintainer_email='<EMAIL>',
    description='TODO: Package description',
    license='TODO: License declaration',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'panda_sim = mujoco_sim.panda_sim:main',
            'init_panda = mujoco_sim.panda_init:main'
        ],
    },
)
