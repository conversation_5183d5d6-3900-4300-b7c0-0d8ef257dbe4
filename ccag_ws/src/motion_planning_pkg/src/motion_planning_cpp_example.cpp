#include <rclcpp/rclcpp.hpp>
#include <ament_index_cpp/get_package_share_directory.hpp>
#include <filesystem>
#include <string>

class MotionPlanningCpp : public rclcpp::Node {
public:
    MotionPlanningCpp() : Node("motion_planning_cpp") {
        // Python 代码转换为 C++:
        // from ament_index_python.packages import get_package_share_directory
        // self.package_dir = get_package_share_directory('mujoco_sim')
        
        // C++ 等价代码:
        package_dir_ = ament_index_cpp::get_package_share_directory("mujoco_sim");
        
        // 声明参数，从launch文件中读取
        this->declare_parameter("dt", 0.001);
        this->declare_parameter("model_dir", std::filesystem::path(package_dir_) / "descriptions");
        
        // 获取参数值
        dt_ = this->get_parameter("dt").as_double();
        model_dir_ = this->get_parameter("model_dir").as_string();
        
        // 打印参数值用于调试
        RCLCPP_INFO(this->get_logger(), "Simulation timestep (dt): %f", dt_);
        RCLCPP_INFO(this->get_logger(), "Model directory: %s", model_dir_.c_str());
        RCLCPP_INFO(this->get_logger(), "Package directory: %s", package_dir_.c_str());
        
        // 验证路径是否存在
        if (std::filesystem::exists(model_dir_)) {
            RCLCPP_INFO(this->get_logger(), "Model directory exists and is accessible");
        } else {
            RCLCPP_WARN(this->get_logger(), "Model directory does not exist: %s", model_dir_.c_str());
        }
    }

private:
    std::string package_dir_;
    double dt_;
    std::string model_dir_;
};

int main(int argc, char** argv) {
    rclcpp::init(argc, argv);
    auto node = std::make_shared<MotionPlanningCpp>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}
