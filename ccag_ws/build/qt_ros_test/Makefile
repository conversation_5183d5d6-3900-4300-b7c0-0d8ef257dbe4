# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/ccag/ccag_ws/src/qt_ros_test

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/ccag/ccag_ws/build/qt_ros_test

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ccag/ccag_ws/build/qt_ros_test/CMakeFiles /home/<USER>/ccag/ccag_ws/build/qt_ros_test//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ccag/ccag_ws/build/qt_ros_test/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named uninstall

# Build rule for target.
uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 uninstall
.PHONY : uninstall

# fast build rule for target.
uninstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uninstall.dir/build.make CMakeFiles/uninstall.dir/build
.PHONY : uninstall/fast

#=============================================================================
# Target rules for targets named qt_ros_test_uninstall

# Build rule for target.
qt_ros_test_uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qt_ros_test_uninstall
.PHONY : qt_ros_test_uninstall

# fast build rule for target.
qt_ros_test_uninstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test_uninstall.dir/build.make CMakeFiles/qt_ros_test_uninstall.dir/build
.PHONY : qt_ros_test_uninstall/fast

#=============================================================================
# Target rules for targets named qt_ros_test

# Build rule for target.
qt_ros_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qt_ros_test
.PHONY : qt_ros_test

# fast build rule for target.
qt_ros_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/build
.PHONY : qt_ros_test/fast

include/qt_ros_test/moc_CCtrlDashBoard.o: include/qt_ros_test/moc_CCtrlDashBoard.cpp.o
.PHONY : include/qt_ros_test/moc_CCtrlDashBoard.o

# target to build an object file
include/qt_ros_test/moc_CCtrlDashBoard.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/include/qt_ros_test/moc_CCtrlDashBoard.cpp.o
.PHONY : include/qt_ros_test/moc_CCtrlDashBoard.cpp.o

include/qt_ros_test/moc_CCtrlDashBoard.i: include/qt_ros_test/moc_CCtrlDashBoard.cpp.i
.PHONY : include/qt_ros_test/moc_CCtrlDashBoard.i

# target to preprocess a source file
include/qt_ros_test/moc_CCtrlDashBoard.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/include/qt_ros_test/moc_CCtrlDashBoard.cpp.i
.PHONY : include/qt_ros_test/moc_CCtrlDashBoard.cpp.i

include/qt_ros_test/moc_CCtrlDashBoard.s: include/qt_ros_test/moc_CCtrlDashBoard.cpp.s
.PHONY : include/qt_ros_test/moc_CCtrlDashBoard.s

# target to generate assembly for a file
include/qt_ros_test/moc_CCtrlDashBoard.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/include/qt_ros_test/moc_CCtrlDashBoard.cpp.s
.PHONY : include/qt_ros_test/moc_CCtrlDashBoard.cpp.s

include/qt_ros_test/moc_main_window.o: include/qt_ros_test/moc_main_window.cpp.o
.PHONY : include/qt_ros_test/moc_main_window.o

# target to build an object file
include/qt_ros_test/moc_main_window.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/include/qt_ros_test/moc_main_window.cpp.o
.PHONY : include/qt_ros_test/moc_main_window.cpp.o

include/qt_ros_test/moc_main_window.i: include/qt_ros_test/moc_main_window.cpp.i
.PHONY : include/qt_ros_test/moc_main_window.i

# target to preprocess a source file
include/qt_ros_test/moc_main_window.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/include/qt_ros_test/moc_main_window.cpp.i
.PHONY : include/qt_ros_test/moc_main_window.cpp.i

include/qt_ros_test/moc_main_window.s: include/qt_ros_test/moc_main_window.cpp.s
.PHONY : include/qt_ros_test/moc_main_window.s

# target to generate assembly for a file
include/qt_ros_test/moc_main_window.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/include/qt_ros_test/moc_main_window.cpp.s
.PHONY : include/qt_ros_test/moc_main_window.cpp.s

include/qt_ros_test/moc_qnode.o: include/qt_ros_test/moc_qnode.cpp.o
.PHONY : include/qt_ros_test/moc_qnode.o

# target to build an object file
include/qt_ros_test/moc_qnode.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/include/qt_ros_test/moc_qnode.cpp.o
.PHONY : include/qt_ros_test/moc_qnode.cpp.o

include/qt_ros_test/moc_qnode.i: include/qt_ros_test/moc_qnode.cpp.i
.PHONY : include/qt_ros_test/moc_qnode.i

# target to preprocess a source file
include/qt_ros_test/moc_qnode.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/include/qt_ros_test/moc_qnode.cpp.i
.PHONY : include/qt_ros_test/moc_qnode.cpp.i

include/qt_ros_test/moc_qnode.s: include/qt_ros_test/moc_qnode.cpp.s
.PHONY : include/qt_ros_test/moc_qnode.s

# target to generate assembly for a file
include/qt_ros_test/moc_qnode.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/include/qt_ros_test/moc_qnode.cpp.s
.PHONY : include/qt_ros_test/moc_qnode.cpp.s

qrc_images.o: qrc_images.cpp.o
.PHONY : qrc_images.o

# target to build an object file
qrc_images.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/qrc_images.cpp.o
.PHONY : qrc_images.cpp.o

qrc_images.i: qrc_images.cpp.i
.PHONY : qrc_images.i

# target to preprocess a source file
qrc_images.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/qrc_images.cpp.i
.PHONY : qrc_images.cpp.i

qrc_images.s: qrc_images.cpp.s
.PHONY : qrc_images.s

# target to generate assembly for a file
qrc_images.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/qrc_images.cpp.s
.PHONY : qrc_images.cpp.s

src/CCtrlDashBoard.o: src/CCtrlDashBoard.cpp.o
.PHONY : src/CCtrlDashBoard.o

# target to build an object file
src/CCtrlDashBoard.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/src/CCtrlDashBoard.cpp.o
.PHONY : src/CCtrlDashBoard.cpp.o

src/CCtrlDashBoard.i: src/CCtrlDashBoard.cpp.i
.PHONY : src/CCtrlDashBoard.i

# target to preprocess a source file
src/CCtrlDashBoard.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/src/CCtrlDashBoard.cpp.i
.PHONY : src/CCtrlDashBoard.cpp.i

src/CCtrlDashBoard.s: src/CCtrlDashBoard.cpp.s
.PHONY : src/CCtrlDashBoard.s

# target to generate assembly for a file
src/CCtrlDashBoard.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/src/CCtrlDashBoard.cpp.s
.PHONY : src/CCtrlDashBoard.cpp.s

src/main.o: src/main.cpp.o
.PHONY : src/main.o

# target to build an object file
src/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/src/main.cpp.o
.PHONY : src/main.cpp.o

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

src/main_window.o: src/main_window.cpp.o
.PHONY : src/main_window.o

# target to build an object file
src/main_window.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/src/main_window.cpp.o
.PHONY : src/main_window.cpp.o

src/main_window.i: src/main_window.cpp.i
.PHONY : src/main_window.i

# target to preprocess a source file
src/main_window.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/src/main_window.cpp.i
.PHONY : src/main_window.cpp.i

src/main_window.s: src/main_window.cpp.s
.PHONY : src/main_window.s

# target to generate assembly for a file
src/main_window.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/src/main_window.cpp.s
.PHONY : src/main_window.cpp.s

src/qnode.o: src/qnode.cpp.o
.PHONY : src/qnode.o

# target to build an object file
src/qnode.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/src/qnode.cpp.o
.PHONY : src/qnode.cpp.o

src/qnode.i: src/qnode.cpp.i
.PHONY : src/qnode.i

# target to preprocess a source file
src/qnode.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/src/qnode.cpp.i
.PHONY : src/qnode.cpp.i

src/qnode.s: src/qnode.cpp.s
.PHONY : src/qnode.s

# target to generate assembly for a file
src/qnode.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qt_ros_test.dir/build.make CMakeFiles/qt_ros_test.dir/src/qnode.cpp.s
.PHONY : src/qnode.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... qt_ros_test_uninstall"
	@echo "... uninstall"
	@echo "... qt_ros_test"
	@echo "... include/qt_ros_test/moc_CCtrlDashBoard.o"
	@echo "... include/qt_ros_test/moc_CCtrlDashBoard.i"
	@echo "... include/qt_ros_test/moc_CCtrlDashBoard.s"
	@echo "... include/qt_ros_test/moc_main_window.o"
	@echo "... include/qt_ros_test/moc_main_window.i"
	@echo "... include/qt_ros_test/moc_main_window.s"
	@echo "... include/qt_ros_test/moc_qnode.o"
	@echo "... include/qt_ros_test/moc_qnode.i"
	@echo "... include/qt_ros_test/moc_qnode.s"
	@echo "... qrc_images.o"
	@echo "... qrc_images.i"
	@echo "... qrc_images.s"
	@echo "... src/CCtrlDashBoard.o"
	@echo "... src/CCtrlDashBoard.i"
	@echo "... src/CCtrlDashBoard.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
	@echo "... src/main_window.o"
	@echo "... src/main_window.i"
	@echo "... src/main_window.s"
	@echo "... src/qnode.o"
	@echo "... src/qnode.i"
	@echo "... src/qnode.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

