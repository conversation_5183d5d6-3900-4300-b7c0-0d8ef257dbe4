/****************************************************************************
** Meta object code from reading C++ file 'main_window.hpp'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../src/qt_ros_test/include/qt_ros_test/main_window.hpp"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'main_window.hpp' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_qt_ros_test__MainWindow_t {
    QByteArrayData data[36];
    char stringdata0[732];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_qt_ros_test__MainWindow_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_qt_ros_test__MainWindow_t qt_meta_stringdata_qt_ros_test__MainWindow = {
    {
QT_MOC_LITERAL(0, 0, 23), // "qt_ros_test::MainWindow"
QT_MOC_LITERAL(1, 24, 7), // "sigSend"
QT_MOC_LITERAL(2, 32, 0), // ""
QT_MOC_LITERAL(3, 33, 6), // "strMsg"
QT_MOC_LITERAL(4, 40, 24), // "on_actionAbout_triggered"
QT_MOC_LITERAL(5, 65, 12), // "slot_rosOpen"
QT_MOC_LITERAL(6, 78, 12), // "cmd_finished"
QT_MOC_LITERAL(7, 91, 8), // "exitCode"
QT_MOC_LITERAL(8, 100, 20), // "QProcess::ExitStatus"
QT_MOC_LITERAL(9, 121, 10), // "exitStatus"
QT_MOC_LITERAL(10, 132, 19), // "updata_camera_image"
QT_MOC_LITERAL(11, 152, 21), // "updata_detected_image"
QT_MOC_LITERAL(12, 174, 30), // "on_checkBox_opencamera_clicked"
QT_MOC_LITERAL(13, 205, 7), // "checked"
QT_MOC_LITERAL(14, 213, 23), // "on_pushButton_1_clicked"
QT_MOC_LITERAL(15, 237, 23), // "on_pushButton_2_clicked"
QT_MOC_LITERAL(16, 261, 33), // "on_pushButton_charge_pose_cli..."
QT_MOC_LITERAL(17, 295, 23), // "on_pushButton_3_clicked"
QT_MOC_LITERAL(18, 319, 23), // "on_pushButton_4_clicked"
QT_MOC_LITERAL(19, 343, 29), // "on_pushButton_refresh_clicked"
QT_MOC_LITERAL(20, 373, 28), // "on_pushButton_camera_clicked"
QT_MOC_LITERAL(21, 402, 30), // "on_pushButton_detected_clicked"
QT_MOC_LITERAL(22, 433, 28), // "on_pushButton_mujoco_clicked"
QT_MOC_LITERAL(23, 462, 23), // "on_pushButton_5_clicked"
QT_MOC_LITERAL(24, 486, 23), // "on_pushButton_6_clicked"
QT_MOC_LITERAL(25, 510, 23), // "on_pushButton_9_clicked"
QT_MOC_LITERAL(26, 534, 24), // "on_pushButton_10_clicked"
QT_MOC_LITERAL(27, 559, 26), // "on_pushButton_send_clicked"
QT_MOC_LITERAL(28, 586, 29), // "on_lineEdit_cmd_returnPressed"
QT_MOC_LITERAL(29, 616, 24), // "on_quit_button_2_clicked"
QT_MOC_LITERAL(30, 641, 23), // "on_pushButton_7_clicked"
QT_MOC_LITERAL(31, 665, 23), // "on_pushButton_8_clicked"
QT_MOC_LITERAL(32, 689, 23), // "slotConnectStateChanged"
QT_MOC_LITERAL(33, 713, 6), // "bState"
QT_MOC_LITERAL(34, 720, 5), // "strIp"
QT_MOC_LITERAL(35, 726, 5) // "nPort"

    },
    "qt_ros_test::MainWindow\0sigSend\0\0"
    "strMsg\0on_actionAbout_triggered\0"
    "slot_rosOpen\0cmd_finished\0exitCode\0"
    "QProcess::ExitStatus\0exitStatus\0"
    "updata_camera_image\0updata_detected_image\0"
    "on_checkBox_opencamera_clicked\0checked\0"
    "on_pushButton_1_clicked\0on_pushButton_2_clicked\0"
    "on_pushButton_charge_pose_clicked\0"
    "on_pushButton_3_clicked\0on_pushButton_4_clicked\0"
    "on_pushButton_refresh_clicked\0"
    "on_pushButton_camera_clicked\0"
    "on_pushButton_detected_clicked\0"
    "on_pushButton_mujoco_clicked\0"
    "on_pushButton_5_clicked\0on_pushButton_6_clicked\0"
    "on_pushButton_9_clicked\0"
    "on_pushButton_10_clicked\0"
    "on_pushButton_send_clicked\0"
    "on_lineEdit_cmd_returnPressed\0"
    "on_quit_button_2_clicked\0"
    "on_pushButton_7_clicked\0on_pushButton_8_clicked\0"
    "slotConnectStateChanged\0bState\0strIp\0"
    "nPort"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_qt_ros_test__MainWindow[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      26,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,  144,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       4,    0,  147,    2, 0x0a /* Public */,
       5,    0,  148,    2, 0x0a /* Public */,
       6,    2,  149,    2, 0x0a /* Public */,
      10,    1,  154,    2, 0x08 /* Private */,
      11,    1,  157,    2, 0x08 /* Private */,
      12,    1,  160,    2, 0x08 /* Private */,
      14,    1,  163,    2, 0x08 /* Private */,
      15,    1,  166,    2, 0x08 /* Private */,
      16,    1,  169,    2, 0x08 /* Private */,
      17,    1,  172,    2, 0x08 /* Private */,
      18,    1,  175,    2, 0x08 /* Private */,
      19,    0,  178,    2, 0x08 /* Private */,
      20,    0,  179,    2, 0x08 /* Private */,
      21,    0,  180,    2, 0x08 /* Private */,
      22,    0,  181,    2, 0x08 /* Private */,
      23,    1,  182,    2, 0x08 /* Private */,
      24,    1,  185,    2, 0x08 /* Private */,
      25,    1,  188,    2, 0x08 /* Private */,
      26,    1,  191,    2, 0x08 /* Private */,
      27,    1,  194,    2, 0x08 /* Private */,
      28,    0,  197,    2, 0x08 /* Private */,
      29,    1,  198,    2, 0x08 /* Private */,
      30,    0,  201,    2, 0x08 /* Private */,
      31,    0,  202,    2, 0x08 /* Private */,
      32,    3,  203,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int, 0x80000000 | 8,    7,    9,
    QMetaType::Void, QMetaType::QImage,    2,
    QMetaType::Void, QMetaType::QImage,    2,
    QMetaType::Void, QMetaType::Bool,   13,
    QMetaType::Void, QMetaType::Bool,   13,
    QMetaType::Void, QMetaType::Bool,   13,
    QMetaType::Void, QMetaType::Bool,   13,
    QMetaType::Void, QMetaType::Bool,   13,
    QMetaType::Void, QMetaType::Bool,   13,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool,   13,
    QMetaType::Void, QMetaType::Bool,   13,
    QMetaType::Void, QMetaType::Bool,   13,
    QMetaType::Void, QMetaType::Bool,   13,
    QMetaType::Void, QMetaType::Bool,   13,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool,   13,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool, QMetaType::QString, QMetaType::Int,   33,   34,   35,

       0        // eod
};

void qt_ros_test::MainWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<MainWindow *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->sigSend((*reinterpret_cast< QString(*)>(_a[1]))); break;
        case 1: _t->on_actionAbout_triggered(); break;
        case 2: _t->slot_rosOpen(); break;
        case 3: _t->cmd_finished((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< QProcess::ExitStatus(*)>(_a[2]))); break;
        case 4: _t->updata_camera_image((*reinterpret_cast< QImage(*)>(_a[1]))); break;
        case 5: _t->updata_detected_image((*reinterpret_cast< QImage(*)>(_a[1]))); break;
        case 6: _t->on_checkBox_opencamera_clicked((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 7: _t->on_pushButton_1_clicked((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 8: _t->on_pushButton_2_clicked((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 9: _t->on_pushButton_charge_pose_clicked((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 10: _t->on_pushButton_3_clicked((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 11: _t->on_pushButton_4_clicked((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 12: _t->on_pushButton_refresh_clicked(); break;
        case 13: _t->on_pushButton_camera_clicked(); break;
        case 14: _t->on_pushButton_detected_clicked(); break;
        case 15: _t->on_pushButton_mujoco_clicked(); break;
        case 16: _t->on_pushButton_5_clicked((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 17: _t->on_pushButton_6_clicked((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 18: _t->on_pushButton_9_clicked((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 19: _t->on_pushButton_10_clicked((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 20: _t->on_pushButton_send_clicked((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 21: _t->on_lineEdit_cmd_returnPressed(); break;
        case 22: _t->on_quit_button_2_clicked((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 23: _t->on_pushButton_7_clicked(); break;
        case 24: _t->on_pushButton_8_clicked(); break;
        case 25: _t->slotConnectStateChanged((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< QString(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (MainWindow::*)(QString );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&MainWindow::sigSend)) {
                *result = 0;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject qt_ros_test::MainWindow::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_meta_stringdata_qt_ros_test__MainWindow.data,
    qt_meta_data_qt_ros_test__MainWindow,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *qt_ros_test::MainWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *qt_ros_test::MainWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_qt_ros_test__MainWindow.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int qt_ros_test::MainWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 26)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 26;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 26)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 26;
    }
    return _id;
}

// SIGNAL 0
void qt_ros_test::MainWindow::sigSend(QString _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
