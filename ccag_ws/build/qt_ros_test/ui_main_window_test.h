/********************************************************************************
** Form generated from reading UI file 'main_window_test.ui'
**
** Created by: Qt User Interface Compiler version 5.15.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAIN_WINDOW_TEST_H
#define UI_MAIN_WINDOW_TEST_H

#include <QtCore/QLocale>
#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QFrame>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QMenu>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QScrollArea>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindowDesign
{
public:
    QAction *action_Quit;
    QAction *action_Preferences;
    QAction *actionAbout;
    QAction *actionAbout_Qt;
    QWidget *centralwidget;
    QVBoxLayout *verticalLayout_2;
    QVBoxLayout *verticalLayout;
    QHBoxLayout *horizontalLayout;
    QLabel *label_7;
    QLabel *label_8;
    QLabel *label_9;
    QSpacerItem *horizontalSpacer_2;
    QHBoxLayout *horizontalLayout_3;
    QTabWidget *tab_manager;
    QWidget *tab_3;
    QHBoxLayout *horizontalLayout_7;
    QVBoxLayout *verticalLayout_8;
    QHBoxLayout *horizontalLayout_6;
    QVBoxLayout *verticalLayout_7;
    QLabel *label_camera;
    QLabel *label_detected;
    QVBoxLayout *verticalLayout_5;
    QCheckBox *checkBox_opencamera;
    QPushButton *pushButton_refresh;
    QSpacerItem *verticalSpacer_3;
    QComboBox *comboBox_select_camera;
    QPushButton *pushButton_camera;
    QComboBox *comboBox_select_detected;
    QPushButton *pushButton_detected;
    QSpacerItem *verticalSpacer_2;
    QWidget *tab_4;
    QVBoxLayout *verticalLayout_4;
    QTextEdit *textEdit_cmd;
    QHBoxLayout *horizontalLayout_8;
    QLabel *label_11;
    QLabel *label_12;
    QLineEdit *lineEdit_cmd;
    QPushButton *pushButton_send;
    QHBoxLayout *horizontalLayout_16;
    QSpacerItem *horizontalSpacer_7;
    QPushButton *quit_button_2;
    QTabWidget *tabWidget;
    QWidget *tab_2;
    QVBoxLayout *verticalLayout_9;
    QScrollArea *scrollArea;
    QWidget *scrollAreaWidgetContents;
    QVBoxLayout *verticalLayout_10;
    QPushButton *pushButton_1;
    QPushButton *pushButton_2;
    QPushButton *pushButton_3;
    QPushButton *pushButton_4;
    QPushButton *pushButton_5;
    QPushButton *pushButton_6;
    QPushButton *pushButton_7;
    QPushButton *pushButton_8;
    QFrame *line;
    QPushButton *pushButton_9;
    QPushButton *pushButton_10;
    QSpacerItem *verticalSpacer;
    QMenuBar *menubar;
    QMenu *menu_File;
    QStatusBar *statusbar;

    void setupUi(QMainWindow *MainWindowDesign)
    {
        if (MainWindowDesign->objectName().isEmpty())
            MainWindowDesign->setObjectName(QString::fromUtf8("MainWindowDesign"));
        MainWindowDesign->resize(1374, 1084);
        QSizePolicy sizePolicy(QSizePolicy::Ignored, QSizePolicy::Ignored);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(MainWindowDesign->sizePolicy().hasHeightForWidth());
        MainWindowDesign->setSizePolicy(sizePolicy);
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/images/icon.png"), QSize(), QIcon::Normal, QIcon::Off);
        MainWindowDesign->setWindowIcon(icon);
        MainWindowDesign->setLocale(QLocale(QLocale::English, QLocale::Australia));
        action_Quit = new QAction(MainWindowDesign);
        action_Quit->setObjectName(QString::fromUtf8("action_Quit"));
        action_Quit->setShortcutContext(Qt::ApplicationShortcut);
        action_Preferences = new QAction(MainWindowDesign);
        action_Preferences->setObjectName(QString::fromUtf8("action_Preferences"));
        actionAbout = new QAction(MainWindowDesign);
        actionAbout->setObjectName(QString::fromUtf8("actionAbout"));
        actionAbout_Qt = new QAction(MainWindowDesign);
        actionAbout_Qt->setObjectName(QString::fromUtf8("actionAbout_Qt"));
        centralwidget = new QWidget(MainWindowDesign);
        centralwidget->setObjectName(QString::fromUtf8("centralwidget"));
        verticalLayout_2 = new QVBoxLayout(centralwidget);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        verticalLayout_2->setSizeConstraint(QLayout::SetNoConstraint);
        verticalLayout = new QVBoxLayout();
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        verticalLayout->setSizeConstraint(QLayout::SetNoConstraint);
        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        label_7 = new QLabel(centralwidget);
        label_7->setObjectName(QString::fromUtf8("label_7"));
        label_7->setMinimumSize(QSize(25, 25));
        label_7->setPixmap(QPixmap(QString::fromUtf8(":/images/robot1.png")));

        horizontalLayout->addWidget(label_7);

        label_8 = new QLabel(centralwidget);
        label_8->setObjectName(QString::fromUtf8("label_8"));
        label_8->setLocale(QLocale(QLocale::Chinese, QLocale::China));

        horizontalLayout->addWidget(label_8);

        label_9 = new QLabel(centralwidget);
        label_9->setObjectName(QString::fromUtf8("label_9"));
        label_9->setMinimumSize(QSize(16, 16));
        label_9->setPixmap(QPixmap(QString::fromUtf8(":/images/false.png")));

        horizontalLayout->addWidget(label_9);

        horizontalSpacer_2 = new QSpacerItem(13, 17, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_2);


        verticalLayout->addLayout(horizontalLayout);

        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setSpacing(2);
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        horizontalLayout_3->setSizeConstraint(QLayout::SetNoConstraint);
        tab_manager = new QTabWidget(centralwidget);
        tab_manager->setObjectName(QString::fromUtf8("tab_manager"));
        sizePolicy.setHeightForWidth(tab_manager->sizePolicy().hasHeightForWidth());
        tab_manager->setSizePolicy(sizePolicy);
        tab_manager->setMinimumSize(QSize(100, 0));
        tab_manager->setLocale(QLocale(QLocale::English, QLocale::Australia));
        tab_3 = new QWidget();
        tab_3->setObjectName(QString::fromUtf8("tab_3"));
        horizontalLayout_7 = new QHBoxLayout(tab_3);
        horizontalLayout_7->setObjectName(QString::fromUtf8("horizontalLayout_7"));
        horizontalLayout_7->setSizeConstraint(QLayout::SetNoConstraint);
        verticalLayout_8 = new QVBoxLayout();
        verticalLayout_8->setSpacing(4);
        verticalLayout_8->setObjectName(QString::fromUtf8("verticalLayout_8"));
        verticalLayout_8->setSizeConstraint(QLayout::SetNoConstraint);
        horizontalLayout_6 = new QHBoxLayout();
        horizontalLayout_6->setSpacing(0);
        horizontalLayout_6->setObjectName(QString::fromUtf8("horizontalLayout_6"));
        horizontalLayout_6->setSizeConstraint(QLayout::SetDefaultConstraint);
        verticalLayout_7 = new QVBoxLayout();
        verticalLayout_7->setSpacing(6);
        verticalLayout_7->setObjectName(QString::fromUtf8("verticalLayout_7"));
        label_camera = new QLabel(tab_3);
        label_camera->setObjectName(QString::fromUtf8("label_camera"));
        label_camera->setTextFormat(Qt::AutoText);
        label_camera->setAlignment(Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop);

        verticalLayout_7->addWidget(label_camera);

        label_detected = new QLabel(tab_3);
        label_detected->setObjectName(QString::fromUtf8("label_detected"));
        label_detected->setAlignment(Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop);

        verticalLayout_7->addWidget(label_detected);


        horizontalLayout_6->addLayout(verticalLayout_7);

        verticalLayout_5 = new QVBoxLayout();
        verticalLayout_5->setSpacing(10);
        verticalLayout_5->setObjectName(QString::fromUtf8("verticalLayout_5"));
        checkBox_opencamera = new QCheckBox(tab_3);
        checkBox_opencamera->setObjectName(QString::fromUtf8("checkBox_opencamera"));
        checkBox_opencamera->setMinimumSize(QSize(150, 36));
        checkBox_opencamera->setMaximumSize(QSize(16777215, 16777215));
        checkBox_opencamera->setLayoutDirection(Qt::LeftToRight);

        verticalLayout_5->addWidget(checkBox_opencamera, 0, Qt::AlignRight);

        pushButton_refresh = new QPushButton(tab_3);
        pushButton_refresh->setObjectName(QString::fromUtf8("pushButton_refresh"));
        pushButton_refresh->setMinimumSize(QSize(150, 36));
        pushButton_refresh->setMaximumSize(QSize(16777215, 16777215));

        verticalLayout_5->addWidget(pushButton_refresh, 0, Qt::AlignRight);

        verticalSpacer_3 = new QSpacerItem(20, 50, QSizePolicy::Minimum, QSizePolicy::Fixed);

        verticalLayout_5->addItem(verticalSpacer_3);

        comboBox_select_camera = new QComboBox(tab_3);
        comboBox_select_camera->setObjectName(QString::fromUtf8("comboBox_select_camera"));
        comboBox_select_camera->setMinimumSize(QSize(150, 36));
        comboBox_select_camera->setMaximumSize(QSize(16777215, 16777215));

        verticalLayout_5->addWidget(comboBox_select_camera, 0, Qt::AlignRight);

        pushButton_camera = new QPushButton(tab_3);
        pushButton_camera->setObjectName(QString::fromUtf8("pushButton_camera"));
        pushButton_camera->setMinimumSize(QSize(150, 36));
        pushButton_camera->setMaximumSize(QSize(16777215, 16777215));

        verticalLayout_5->addWidget(pushButton_camera);

        comboBox_select_detected = new QComboBox(tab_3);
        comboBox_select_detected->setObjectName(QString::fromUtf8("comboBox_select_detected"));
        comboBox_select_detected->setMinimumSize(QSize(150, 36));

        verticalLayout_5->addWidget(comboBox_select_detected);

        pushButton_detected = new QPushButton(tab_3);
        pushButton_detected->setObjectName(QString::fromUtf8("pushButton_detected"));
        pushButton_detected->setCheckable(true);

        verticalLayout_5->addWidget(pushButton_detected);

        verticalSpacer_2 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_5->addItem(verticalSpacer_2);


        horizontalLayout_6->addLayout(verticalLayout_5);

        horizontalLayout_6->setStretch(0, 3);

        verticalLayout_8->addLayout(horizontalLayout_6);

        verticalLayout_8->setStretch(0, 1);

        horizontalLayout_7->addLayout(verticalLayout_8);

        tab_manager->addTab(tab_3, QString());
        tab_4 = new QWidget();
        tab_4->setObjectName(QString::fromUtf8("tab_4"));
        verticalLayout_4 = new QVBoxLayout(tab_4);
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        textEdit_cmd = new QTextEdit(tab_4);
        textEdit_cmd->setObjectName(QString::fromUtf8("textEdit_cmd"));
        textEdit_cmd->setMouseTracking(true);
        textEdit_cmd->setReadOnly(true);

        verticalLayout_4->addWidget(textEdit_cmd);

        horizontalLayout_8 = new QHBoxLayout();
        horizontalLayout_8->setObjectName(QString::fromUtf8("horizontalLayout_8"));
        label_11 = new QLabel(tab_4);
        label_11->setObjectName(QString::fromUtf8("label_11"));

        horizontalLayout_8->addWidget(label_11);

        label_12 = new QLabel(tab_4);
        label_12->setObjectName(QString::fromUtf8("label_12"));

        horizontalLayout_8->addWidget(label_12);

        lineEdit_cmd = new QLineEdit(tab_4);
        lineEdit_cmd->setObjectName(QString::fromUtf8("lineEdit_cmd"));

        horizontalLayout_8->addWidget(lineEdit_cmd);

        pushButton_send = new QPushButton(tab_4);
        pushButton_send->setObjectName(QString::fromUtf8("pushButton_send"));
        pushButton_send->setAutoRepeat(false);

        horizontalLayout_8->addWidget(pushButton_send);


        verticalLayout_4->addLayout(horizontalLayout_8);

        horizontalLayout_16 = new QHBoxLayout();
        horizontalLayout_16->setObjectName(QString::fromUtf8("horizontalLayout_16"));
        horizontalSpacer_7 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_16->addItem(horizontalSpacer_7);

        quit_button_2 = new QPushButton(tab_4);
        quit_button_2->setObjectName(QString::fromUtf8("quit_button_2"));
        QSizePolicy sizePolicy1(QSizePolicy::MinimumExpanding, QSizePolicy::Fixed);
        sizePolicy1.setHorizontalStretch(0);
        sizePolicy1.setVerticalStretch(0);
        sizePolicy1.setHeightForWidth(quit_button_2->sizePolicy().hasHeightForWidth());
        quit_button_2->setSizePolicy(sizePolicy1);

        horizontalLayout_16->addWidget(quit_button_2);

        horizontalLayout_16->setStretch(0, 1);

        verticalLayout_4->addLayout(horizontalLayout_16);

        tab_manager->addTab(tab_4, QString());

        horizontalLayout_3->addWidget(tab_manager);

        tabWidget = new QTabWidget(centralwidget);
        tabWidget->setObjectName(QString::fromUtf8("tabWidget"));
        tab_2 = new QWidget();
        tab_2->setObjectName(QString::fromUtf8("tab_2"));
        verticalLayout_9 = new QVBoxLayout(tab_2);
        verticalLayout_9->setObjectName(QString::fromUtf8("verticalLayout_9"));
        scrollArea = new QScrollArea(tab_2);
        scrollArea->setObjectName(QString::fromUtf8("scrollArea"));
        scrollArea->setWidgetResizable(true);
        scrollAreaWidgetContents = new QWidget();
        scrollAreaWidgetContents->setObjectName(QString::fromUtf8("scrollAreaWidgetContents"));
        scrollAreaWidgetContents->setGeometry(QRect(0, 0, 421, 902));
        verticalLayout_10 = new QVBoxLayout(scrollAreaWidgetContents);
        verticalLayout_10->setObjectName(QString::fromUtf8("verticalLayout_10"));
        pushButton_1 = new QPushButton(scrollAreaWidgetContents);
        pushButton_1->setObjectName(QString::fromUtf8("pushButton_1"));
        pushButton_1->setCheckable(true);
        pushButton_1->setChecked(false);
        pushButton_1->setAutoRepeat(false);

        verticalLayout_10->addWidget(pushButton_1);

        pushButton_2 = new QPushButton(scrollAreaWidgetContents);
        pushButton_2->setObjectName(QString::fromUtf8("pushButton_2"));
        pushButton_2->setCheckable(true);
        pushButton_2->setChecked(false);
        pushButton_2->setAutoRepeat(false);

        verticalLayout_10->addWidget(pushButton_2);

        pushButton_3 = new QPushButton(scrollAreaWidgetContents);
        pushButton_3->setObjectName(QString::fromUtf8("pushButton_3"));
        pushButton_3->setCheckable(true);
        pushButton_3->setChecked(false);
        pushButton_3->setAutoRepeat(false);

        verticalLayout_10->addWidget(pushButton_3);

        pushButton_4 = new QPushButton(scrollAreaWidgetContents);
        pushButton_4->setObjectName(QString::fromUtf8("pushButton_4"));
        pushButton_4->setCheckable(true);
        pushButton_4->setChecked(false);
        pushButton_4->setAutoRepeat(false);

        verticalLayout_10->addWidget(pushButton_4);

        pushButton_5 = new QPushButton(scrollAreaWidgetContents);
        pushButton_5->setObjectName(QString::fromUtf8("pushButton_5"));
        pushButton_5->setCheckable(true);
        pushButton_5->setChecked(false);
        pushButton_5->setAutoRepeat(false);

        verticalLayout_10->addWidget(pushButton_5);

        pushButton_6 = new QPushButton(scrollAreaWidgetContents);
        pushButton_6->setObjectName(QString::fromUtf8("pushButton_6"));
        pushButton_6->setCheckable(true);
        pushButton_6->setChecked(false);
        pushButton_6->setAutoRepeat(false);

        verticalLayout_10->addWidget(pushButton_6);

        pushButton_7 = new QPushButton(scrollAreaWidgetContents);
        pushButton_7->setObjectName(QString::fromUtf8("pushButton_7"));
        pushButton_7->setCheckable(true);
        pushButton_7->setChecked(false);
        pushButton_7->setAutoRepeat(false);

        verticalLayout_10->addWidget(pushButton_7);

        pushButton_8 = new QPushButton(scrollAreaWidgetContents);
        pushButton_8->setObjectName(QString::fromUtf8("pushButton_8"));
        pushButton_8->setCheckable(true);
        pushButton_8->setChecked(false);
        pushButton_8->setAutoRepeat(false);

        verticalLayout_10->addWidget(pushButton_8);

        line = new QFrame(scrollAreaWidgetContents);
        line->setObjectName(QString::fromUtf8("line"));
        QSizePolicy sizePolicy2(QSizePolicy::Minimum, QSizePolicy::Expanding);
        sizePolicy2.setHorizontalStretch(0);
        sizePolicy2.setVerticalStretch(0);
        sizePolicy2.setHeightForWidth(line->sizePolicy().hasHeightForWidth());
        line->setSizePolicy(sizePolicy2);
        line->setMaximumSize(QSize(16777215, 40));
        line->setSizeIncrement(QSize(0, 0));
        line->setBaseSize(QSize(0, 0));
        line->setLineWidth(1);
        line->setMidLineWidth(0);
        line->setFrameShape(QFrame::HLine);
        line->setFrameShadow(QFrame::Sunken);

        verticalLayout_10->addWidget(line);

        pushButton_9 = new QPushButton(scrollAreaWidgetContents);
        pushButton_9->setObjectName(QString::fromUtf8("pushButton_9"));
        pushButton_9->setCheckable(true);

        verticalLayout_10->addWidget(pushButton_9);

        pushButton_10 = new QPushButton(scrollAreaWidgetContents);
        pushButton_10->setObjectName(QString::fromUtf8("pushButton_10"));
        pushButton_10->setCheckable(true);
        pushButton_10->setChecked(false);
        pushButton_10->setAutoRepeat(false);

        verticalLayout_10->addWidget(pushButton_10);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_10->addItem(verticalSpacer);

        scrollArea->setWidget(scrollAreaWidgetContents);

        verticalLayout_9->addWidget(scrollArea);

        tabWidget->addTab(tab_2, QString());

        horizontalLayout_3->addWidget(tabWidget);

        horizontalLayout_3->setStretch(0, 2);
        horizontalLayout_3->setStretch(1, 1);

        verticalLayout->addLayout(horizontalLayout_3);


        verticalLayout_2->addLayout(verticalLayout);

        MainWindowDesign->setCentralWidget(centralwidget);
        menubar = new QMenuBar(MainWindowDesign);
        menubar->setObjectName(QString::fromUtf8("menubar"));
        menubar->setGeometry(QRect(0, 0, 1374, 32));
        menu_File = new QMenu(menubar);
        menu_File->setObjectName(QString::fromUtf8("menu_File"));
        MainWindowDesign->setMenuBar(menubar);
        statusbar = new QStatusBar(MainWindowDesign);
        statusbar->setObjectName(QString::fromUtf8("statusbar"));
        MainWindowDesign->setStatusBar(statusbar);

        menubar->addAction(menu_File->menuAction());
        menu_File->addAction(action_Preferences);
        menu_File->addSeparator();
        menu_File->addAction(actionAbout);
        menu_File->addAction(actionAbout_Qt);
        menu_File->addSeparator();
        menu_File->addAction(action_Quit);

        retranslateUi(MainWindowDesign);
        QObject::connect(action_Quit, SIGNAL(triggered()), MainWindowDesign, SLOT(close()));

        tab_manager->setCurrentIndex(0);
        tabWidget->setCurrentIndex(0);


        QMetaObject::connectSlotsByName(MainWindowDesign);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindowDesign)
    {
        MainWindowDesign->setWindowTitle(QCoreApplication::translate("MainWindowDesign", "QRosApp", nullptr));
        action_Quit->setText(QCoreApplication::translate("MainWindowDesign", "&Quit", nullptr));
#if QT_CONFIG(shortcut)
        action_Quit->setShortcut(QCoreApplication::translate("MainWindowDesign", "Ctrl+Q", nullptr));
#endif // QT_CONFIG(shortcut)
        action_Preferences->setText(QCoreApplication::translate("MainWindowDesign", "&Preferences", nullptr));
        actionAbout->setText(QCoreApplication::translate("MainWindowDesign", "&About", nullptr));
        actionAbout_Qt->setText(QCoreApplication::translate("MainWindowDesign", "About &Qt", nullptr));
        label_7->setText(QString());
        label_8->setText(QCoreApplication::translate("MainWindowDesign", "\347\246\273\347\272\277", nullptr));
        label_9->setText(QString());
        label_camera->setText(QCoreApplication::translate("MainWindowDesign", "label_camera", nullptr));
        label_detected->setText(QCoreApplication::translate("MainWindowDesign", "label_detected", nullptr));
        checkBox_opencamera->setText(QCoreApplication::translate("MainWindowDesign", "\346\211\223\345\274\200\346\221\204\345\203\217\345\244\264", nullptr));
        pushButton_refresh->setText(QCoreApplication::translate("MainWindowDesign", "\345\210\267\346\226\260\350\257\235\351\242\230", nullptr));
        pushButton_camera->setText(QCoreApplication::translate("MainWindowDesign", "\345\233\276\345\203\217\350\256\242\351\230\205", nullptr));
        pushButton_detected->setText(QCoreApplication::translate("MainWindowDesign", "\345\233\276\345\203\217\350\256\242\351\230\205", nullptr));
        tab_manager->setTabText(tab_manager->indexOf(tab_3), QCoreApplication::translate("MainWindowDesign", "\345\237\272\346\234\254", nullptr));
        label_11->setText(QCoreApplication::translate("MainWindowDesign", "\350\276\223\345\205\245\345\221\275\344\273\244\357\274\232", nullptr));
        label_12->setText(QCoreApplication::translate("MainWindowDesign", "robot@robot", nullptr));
        pushButton_send->setText(QCoreApplication::translate("MainWindowDesign", "\345\217\221\351\200\201", nullptr));
        quit_button_2->setText(QCoreApplication::translate("MainWindowDesign", "\347\273\223\346\235\237\350\277\220\350\241\214", nullptr));
        tab_manager->setTabText(tab_manager->indexOf(tab_4), QCoreApplication::translate("MainWindowDesign", "\345\221\275\344\273\244\350\241\214", nullptr));
        pushButton_1->setText(QCoreApplication::translate("MainWindowDesign", "1.\345\217\226\346\212\242", nullptr));
        pushButton_2->setText(QCoreApplication::translate("MainWindowDesign", "2.\345\205\205\347\224\265\345\217\243\346\243\200\346\265\213", nullptr));
        pushButton_3->setText(QCoreApplication::translate("MainWindowDesign", "3.\347\262\227\345\256\232\344\275\215", nullptr));
        pushButton_4->setText(QCoreApplication::translate("MainWindowDesign", "4.\347\262\276\345\256\232\344\275\215", nullptr));
        pushButton_5->setText(QCoreApplication::translate("MainWindowDesign", "5.\346\217\222\346\212\242", nullptr));
        pushButton_6->setText(QCoreApplication::translate("MainWindowDesign", "6.\346\213\224\346\236\252", nullptr));
        pushButton_7->setText(QCoreApplication::translate("MainWindowDesign", "7.\345\205\205\347\224\265\346\236\252\345\244\215\344\275\215", nullptr));
        pushButton_8->setText(QCoreApplication::translate("MainWindowDesign", "8.\346\234\272\346\242\260\350\207\202\345\244\215\344\275\215", nullptr));
        pushButton_9->setText(QCoreApplication::translate("MainWindowDesign", "9.\344\270\200\351\224\256\345\205\205\347\224\265", nullptr));
        pushButton_10->setText(QCoreApplication::translate("MainWindowDesign", "10.\344\270\200\351\224\256\345\244\215\344\275\215", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(tab_2), QCoreApplication::translate("MainWindowDesign", "\345\277\253\346\215\267\345\212\237\350\203\275", nullptr));
        menu_File->setTitle(QCoreApplication::translate("MainWindowDesign", "&App", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MainWindowDesign: public Ui_MainWindowDesign {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAIN_WINDOW_TEST_H
