{"artifacts": [{"path": "qt_ros_test"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "link_directories", "target_link_libraries", "ament_target_dependencies", "add_compile_options", "include_directories", "target_include_directories"], "files": ["CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 60, "parent": 0}, {"command": 1, "file": 0, "line": 63, "parent": 0}, {"command": 2, "file": 0, "line": 42, "parent": 0}, {"command": 3, "file": 0, "line": 61, "parent": 0}, {"command": 4, "file": 0, "line": 62, "parent": 0}, {"command": 3, "file": 1, "line": 145, "parent": 5}, {"command": 5, "file": 0, "line": 15, "parent": 0}, {"command": 6, "file": 0, "line": 36, "parent": 0}, {"command": 7, "file": 1, "line": 141, "parent": 5}]}, "compileGroups": [{"compileCommandFragments": [{"backtrace": 7, "fragment": "-Wall"}, {"backtrace": 7, "fragment": "-Wextra"}, {"backtrace": 7, "fragment": "-Wpedantic"}, {"backtrace": 7, "fragment": "-Wno-unused-parameter"}, {"backtrace": 7, "fragment": "-g"}, {"backtrace": 4, "fragment": "-fPIC"}], "defines": [{"backtrace": 6, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 4, "define": "QT_CORE_LIB"}, {"backtrace": 4, "define": "QT_GUI_LIB"}, {"backtrace": 4, "define": "QT_NO_DEBUG"}, {"backtrace": 4, "define": "QT_WIDGETS_LIB"}, {"backtrace": 6, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}], "includes": [{"backtrace": 8, "path": "/home/<USER>/ccag/ccag_ws/src/qt_ros_test/include"}, {"backtrace": 8, "path": "/home/<USER>/ccag/ccag_ws/build/qt_ros_test"}, {"backtrace": 8, "path": "/home/<USER>/ccag/ccag_ws/src/qt_ros_test/include/qt_ros_test"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/nav_msgs"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/cv_bridge"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/image_transport"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt5"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt5/QtWidgets"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt5/QtGui"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt5/QtCore"}, {"backtrace": 4, "isSystem": true, "path": "/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/opencv4"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/ament_index_cpp"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/libstatistics_collector"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rcl"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rcl_interfaces"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rcl_logging_interface"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rcl_yaml_param_parser"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/libyaml_vendor"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/tracetools"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rcpputils"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/statistics_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rosgraph_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_cpp"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/message_filters"}], "language": "CXX", "languageStandard": {"backtraces": [4, 4, 6], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 7, 8, 9]}], "id": "qt_ros_test::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib/qt_ros_test"}], "prefix": {"path": "/home/<USER>/ccag/ccag_ws/install/qt_ros_test"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"backtrace": 3, "fragment": "-L/home/<USER>/ccag/ccag_ws/src/qt_ros_test/lib", "role": "libraryPath"}, {"backtrace": 3, "fragment": "-L/home/<USER>/ccag/ccag_ws/build/qt_ros_test/lib", "role": "libraryPath"}, {"fragment": "-Wl,-r<PERSON>,/home/<USER>/ccag/ccag_ws/src/qt_ros_test/lib:/home/<USER>/ccag/ccag_ws/build/qt_ros_test/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libQt5Widgets.so.5.15.3", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_stitching.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_alphamat.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_aruco.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_barcode.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_bgsegm.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_bioinspired.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_ccalib.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dnn_objdetect.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dnn_superres.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dpm.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_face.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_freetype.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_fuzzy.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_hdf.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_hfs.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_img_hash.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_intensity_transform.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_line_descriptor.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_mcc.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_quality.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_rapid.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_reg.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_rgbd.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_saliency.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_shape.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_stereo.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_structured_light.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_superres.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_surface_matching.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_tracking.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_videostab.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_viz.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_wechat_qrcode.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_xobjdetect.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_xphoto.so.4.5.4d", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libcv_bridge.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/x86_64-linux-gnu/libimage_transport.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libQt5Gui.so.5.15.3", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libQt5Core.so.5.15.3", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_highgui.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_datasets.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_plot.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_text.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_ml.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_phase_unwrapping.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_optflow.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_ximgproc.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_video.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_videoio.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_objdetect.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_calib3d.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dnn.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_features2d.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_flann.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_photo.so.4.5.4d", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_imgcodecs.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_imgproc.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_core.so.4.5.4d", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libmessage_filters.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/liblibstatistics_collector.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librmw_implementation.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libament_index_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_logging_spdlog.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_logging_interface.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libyaml.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtracetools.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"fragment": "-ldl", "role": "libraries"}], "language": "CXX"}, "name": "qt_ros_test", "nameOnDisk": "qt_ros_test", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 7, 8, 9]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [5, 6]}, {"name": "", "sourceIndexes": [10, 11, 12]}, {"name": "CMake Rules", "sourceIndexes": [13, 14, 15]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/CCtrlDashBoard.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main_window.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/qnode.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/qt_ros_test/qrc_images.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/qt_ros_test/ui_main_window.h", "sourceGroupIndex": 1}, {"backtrace": 1, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/qt_ros_test/ui_main_window_test.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/qt_ros_test/include/qt_ros_test/moc_CCtrlDashBoard.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/qt_ros_test/include/qt_ros_test/moc_main_window.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/qt_ros_test/include/qt_ros_test/moc_qnode.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "path": "resources/images.qrc", "sourceGroupIndex": 2}, {"backtrace": 0, "path": "ui/main_window.ui", "sourceGroupIndex": 2}, {"backtrace": 0, "path": "ui/main_window_test.ui", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/qt_ros_test/include/qt_ros_test/moc_CCtrlDashBoard.cpp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/qt_ros_test/include/qt_ros_test/moc_main_window.cpp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/qt_ros_test/include/qt_ros_test/moc_qnode.cpp.rule", "sourceGroupIndex": 3}], "type": "EXECUTABLE"}