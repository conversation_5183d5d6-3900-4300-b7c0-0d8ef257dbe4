{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-01db5720fa7de4bcd713.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "qt_ros_test", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "qt_ros_test::@6890427a1f51a3e7e1df", "jsonFile": "target-qt_ros_test-903d5b9e237914ea8e01.json", "name": "qt_ros_test", "projectIndex": 0}, {"directoryIndex": 0, "id": "qt_ros_test_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-qt_ros_test_uninstall-6862e32ba337febc240e.json", "name": "qt_ros_test_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-e1980ba57cb8545d4a5f.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/ccag/ccag_ws/build/qt_ros_test", "source": "/home/<USER>/ccag/ccag_ws/src/qt_ros_test"}, "version": {"major": 2, "minor": 3}}