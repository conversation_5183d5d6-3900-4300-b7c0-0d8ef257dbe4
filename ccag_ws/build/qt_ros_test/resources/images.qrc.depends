<RCC>
    <qresource prefix="/">
        <file>images/icon.png</file>
        <file>images/power-v.png</file>
        <file>images/robot.png</file>
        <file>images/false.png</file>
        <file>images/ok.png</file>
        <file>images/robot1.png</file>
        <file>images/classes/Axes.png</file>
        <file>images/classes/Camera.png</file>
        <file>images/classes/DepthCloud.png</file>
        <file>images/classes/Displays.svg</file>
        <file>images/classes/Effort.png</file>
        <file>images/classes/FluidPressure.png</file>
        <file>images/classes/FocusCamera.svg</file>
        <file>images/classes/Grid.png</file>
        <file>images/classes/GridCells.png</file>
        <file>images/classes/Group.png</file>
        <file>images/classes/Help.svg</file>
        <file>images/classes/Illuminance.png</file>
        <file>images/classes/Image.png</file>
        <file>images/classes/Interact.png</file>
        <file>images/classes/InteractiveMarkers.png</file>
        <file>images/classes/LaserScan.png</file>
        <file>images/classes/Map.png</file>
        <file>images/classes/Marker.png</file>
        <file>images/classes/MarkerArray.png</file>
        <file>images/classes/Measure.svg</file>
        <file>images/classes/MoveCamera.png</file>
        <file>images/classes/Odometry.png</file>
        <file>images/classes/Path.png</file>
        <file>images/classes/PointCloud.png</file>
        <file>images/classes/PointCloud2.png</file>
        <file>images/classes/PointStamped.png</file>
        <file>images/classes/Polygon.png</file>
        <file>images/classes/Pose.png</file>
        <file>images/classes/PoseArray.png</file>
        <file>images/classes/PoseWithCovariance.png</file>
        <file>images/classes/PublishPoint.svg</file>
        <file>images/classes/Range.png</file>
        <file>images/classes/RelativeHumidity.png</file>
        <file>images/classes/RobotJoint.png</file>
        <file>images/classes/RobotLink.png</file>
        <file>images/classes/RobotLinkNoGeom.png</file>
        <file>images/classes/RobotModel.png</file>
        <file>images/classes/Select.png</file>
        <file>images/classes/Selection.png</file>
        <file>images/classes/SetGoal.png</file>
        <file>images/classes/SetInitialPose.png</file>
        <file>images/classes/Temperature.png</file>
        <file>images/classes/TF.png</file>
        <file>images/classes/Time.svg</file>
        <file>images/classes/Tool Properties.png</file>
        <file>images/classes/Views.svg</file>
        <file>images/classes/WrenchStamped.png</file>
        <file>images/classes/src/Arrow.xcf</file>
        <file>images/classes/src/Axes.xcf</file>
        <file>images/classes/src/GridCells.xcf</file>
        <file>images/classes/src/InteractiveMarker.xcf</file>
        <file>images/classes/src/Map.xcf</file>
        <file>images/classes/src/MarkerArray.xcf</file>
        <file>images/classes/src/Poly.xcf</file>
        <file>images/classes/src/Polygon.xcf</file>
        <file>images/classes/src/PoseArray.xcf</file>
        <file>images/classes/src/Range.xcf</file>
        <file>images/classes/src/RobotModel.xcf</file>
        <file>images/classes/src/TF.xcf</file>
        <file>images/power-l.png</file>
    </qresource>
</RCC>
