{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-ccccd167914c505de923.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "yolo_bringup", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-fabe6c3f532e67d0b8f0.json", "name": "uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "yolo_bringup_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-yolo_bringup_uninstall-94bbc9b4bd92e6e02b7e.json", "name": "yolo_bringup_uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/ccag/ccag_ws/build/yolo_bringup", "source": "/home/<USER>/ccag/ccag_ws/src/yolo_ros/yolo_bringup"}, "version": {"major": 2, "minor": 3}}