import rclpy
from rclpy.node import Node
from geometry_msgs.msg import PoseStamped, TransformStamped
from trajectory_msgs.msg import JointTrajectory, JointTrajectoryPoint
from tf2_ros import TransformBroadcaster
from sensor_msgs.msg import JointState
import mujoco,mujoco_viewer
import numpy as np
import pinocchio
import coal
import os
import threading
import matplotlib.pyplot as plt
import time
# matplotlib.use('TkAgg')
from .utils import (
    set_collisions,
    check_collisions_at_state,
    get_random_collision_free_state,
    extract_cartesian_poses
)
from .nullspace_components import (
    joint_limit_nullspace_component,
    collision_avoidance_nullspace_component
)
from .differential_ik import DifferentialIkOptions, DifferentialIk
from .rrt import RRTPlanner, RRTPlannerOptions
from .trajectory_optimization import CubicTrajectoryOptimizationOptions, CubicTrajectoryOptimization
from scipy.spatial.transform import Rotation as R
from ament_index_python.packages import get_package_share_directory



class MotionPlanning(Node):
    def __init__(self):
        super().__init__('broad_localization_node')
        self.package_dir = get_package_share_directory('mujoco_sim')
        # 声明参数，从launch文件中读取
        self.declare_parameter('dt', 0.001)
        # self.declare_parameter('model_dir', '/home/<USER>/ccag/ccag_ws/src/mujoco_sim/mujoco_sim/descriptions/')
        self.declare_parameter('model_dir', os.path.join(self.package_dir,'descriptions'))
        # 获取参数值
        self.dt = self.get_parameter('dt').get_parameter_value().double_value
        self.model_dir = self.get_parameter('model_dir').get_parameter_value().string_value
        # 打印参数值用于调试
        self.get_logger().info(f'Simulation timestep (dt): {self.dt}')
        self.get_logger().info(f'Model directory: {self.model_dir}')
        
        self.target_pose = None
        self.plot_thread = None  # 用于存储绘图线程对象
        # Subscriber for end-effector pose
        self.pose_subscription = self.create_subscription(
            PoseStamped,
            '/charging_post_pose',
            self.pose_callback,
            10
        )
        # Subscriber for joint states
        # self.joint_state_sub = self.create_subscription(
        #     JointState,
        #     '/joint_states',
        #     self.joint_state_callback,
        #     10
        # )
        # Publisher for generated trajectory
        self.trajectory_publisher = self.create_publisher(
            JointTrajectory,
            '/generated_trajectory',
            10
        )
        
        # TF broadcaster
        # self.tf_broadcaster = TransformBroadcaster(self)
        
        # Timers
        self.trajectory_timer = self.create_timer(1, self.publish_trajectory_timer)
        # self.tf_timer = self.create_timer(0.01, self.publish_tf)  # 100Hz
        self.last_traj_gen = None

    def pose_callback(self, msg):
        """
        Callback function to handle received end-effector pose.

        Parameters
        ----------
        msg : PoseStamped
            The received pose message.
        """
        self.target_pose = msg

    def joint_state_callback(self, msg):
        """
        Callback function to handle received joint states.
        Extracts joint positions and computes forward kinematics.

        Parameters
        ----------
        msg : JointState
            The received joint state message.
        """
        # Extract joint positions (first 7 joints for arm)
        self.current_joint_state = msg
        if len(msg.position) >= 7:  # Ensure we have enough joints
            self.current_joint_positions = np.array(msg.position[:7], dtype=np.float64)

    def publish_tf(self):
        """
        Publish the current end-effector TF at 100Hz using forward kinematics.
        Uses actual joint angles from /joint_states topic.
        """
        if hasattr(self, 'model_roboplan') and hasattr(self, 'data'):
            # Get current joint angles from joint state
            if hasattr(self, 'current_joint_positions'):
                q_cur = np.zeros(self.model_roboplan.nq)
                q_cur[:7] = self.current_joint_positions  # Only use first 7 joints for arm
            else:
                q_cur = np.zeros(self.model_roboplan.nq)
            
            # Compute and publish TF
            tf_msg = self.create_tf_message(q_cur)
            self.tf_broadcaster.sendTransform(tf_msg)

    def create_tf_message(self,q_cur):
        """
        Create a TF message using pinocchio forward kinematics.
        
        Parameters
        ----------
        q_cur : numpy.ndarray
            Current joint angles.
            
        Returns
        -------
        TransformStamped
            The TF message.
        """
        # Compute forward kinematics
        target_frame_id = self.model_roboplan.getFrameId("plug")
        pinocchio.framesForwardKinematics(self.model_roboplan, self.data, q_cur)
        cur_tform = self.data.oMf[target_frame_id]
        
        # Convert to TF message
        tf_msg = TransformStamped()
        tf_msg.header.stamp = self.get_clock().now().to_msg()
        tf_msg.header.frame_id = "base_link"
        tf_msg.child_frame_id = "plug"
        
        # Set translation from pinocchio transform
        tf_msg.transform.translation.x = cur_tform.translation[0]
        tf_msg.transform.translation.y = cur_tform.translation[1]
        tf_msg.transform.translation.z = cur_tform.translation[2]
        
        # Convert rotation matrix to quaternion
        quat = pinocchio.Quaternion(cur_tform.rotation)
        tf_msg.transform.rotation.x = quat.x
        tf_msg.transform.rotation.y = quat.y
        tf_msg.transform.rotation.z = quat.z
        tf_msg.transform.rotation.w = quat.w
        
        return tf_msg
        
    def publish_trajectory_timer(self):
        if self.last_traj_gen is not None:
            self.publish_trajectory(self.last_traj_gen)

    def publish_trajectory(self, traj_gen):
        """
        Publish the generated trajectory.

        Parameters
        ----------
        traj_gen : tuple
            A tuple containing (time_points, q_vec), where:
            - time_points: A list or array of time points corresponding to each q_vec column.
            - q_vec: A 2D array of joint positions (shape: [num_joints, num_points]).
        """
        trajectory_msg = JointTrajectory()
        trajectory_msg.header.stamp = self.get_clock().now().to_msg()
        trajectory_msg.joint_names = [f'joint_{i+1}' for i in range(traj_gen[1].shape[0])]

        for i in range(traj_gen[1].shape[1]):
            point = JointTrajectoryPoint()
            point.positions = traj_gen[1][:, i].tolist()
            point.time_from_start.sec = int(traj_gen[0][i])
            point.time_from_start.nanosec = int((traj_gen[0][i] % 1) * 1e9)
            trajectory_msg.points.append(point)

        self.trajectory_publisher.publish(trajectory_msg)

    def planning(self):
        
        urdf_filepath = os.path.join(self.model_dir, "panda_description", "urdf", "panda.urdf")
        srdf_filepath = os.path.join(self.model_dir, "panda_description", "srdf", "panda.srdf")
        self.model_roboplan, self.collision_model, visual_model = self.load_models(urdf_filepath,self.model_dir)
        self.add_self_collisions(self.model_roboplan, self.collision_model, srdf_filepath)
        self.add_object_collisions(self.model_roboplan, self.collision_model, visual_model, inflation_radius=0.1)

        self.data = self.model_roboplan.createData()
        collision_data = self.collision_model.createData()

        self.target_frame = "plug"
        ignore_joint_indices = [
            # self.model_roboplan.getJointId("panda_finger_joint1") - 1,
            # self.model_roboplan.getJointId("panda_finger_joint2") - 1,
        ]
        np.set_printoptions(precision=3)

        # Set up the IK solver
        options = DifferentialIkOptions(
            max_iters=200,
            max_retries=10,
            damping=0.0001,
            min_step_size=0.05,
            max_step_size=0.1,
            ignore_joint_indices=ignore_joint_indices,
            rng_seed=None,
        )
        self.ik = DifferentialIk(
            self.model_roboplan,
            data=self.data,
            collision_model=self.collision_model,
            options=options,
            visualizer=None,
        )
        self.nullspace_components = [
            lambda model_roboplan, q: collision_avoidance_nullspace_component(
                model_roboplan,
                self.data,
                self.collision_model,
                collision_data,
                q,
                gain=1.0,
                dist_padding=0.05,
            ),
            lambda model_roboplan, q: joint_limit_nullspace_component(
                model_roboplan, q, gain=1.0, padding=0.025
            ),
        ]
        # self.init_pos = [ 0.32, -0.435, -0.31, -2.582, -0.154, 2.356, 0.0]
                        #  [ 0.32, -0.435, -0.31, -2.94, -0.154, 2.81, 0.0]
        q_start = np.array([ 0.32, -0.435, -0.31, -2.94, -0.154, 2.81, 0.0])  # 初始关节角度
        self.q_start = q_start.copy()
        # 检查是否接收到目标位姿
        if self.target_pose is None:
            self.get_logger().error("No target pose received. Cannot compute q_goal.")
            return None

        # 提取目标位姿的位置和四元数
        target_position = [
            self.target_pose.pose.position.x,
            self.target_pose.pose.position.y,
            self.target_pose.pose.position.z
        ]
        target_orientation = [
            self.target_pose.pose.orientation.x,
            self.target_pose.pose.orientation.y,
            self.target_pose.pose.orientation.z,
            self.target_pose.pose.orientation.w
        ]

        # 将四元数转换为旋转矩阵
        rotation_matrix = R.from_quat(target_orientation).as_matrix()

        self.get_logger().info(f"Recieved target position:\n {target_position}")
        self.get_logger().info(f"Recieved target rotation matrix:\n {rotation_matrix}")
        # 使用目标位姿计算 q_goal
        self.get_logger().info("STEP1:Start IK Solution...")
        start_time = time.time()
        self.get_logger().info(f"start state: {q_start}")
        init_state = self.get_valid_init_state(q_start.copy())
        self.get_logger().info(f"Valid initial state: {init_state}")
        q_goal = self.getIk(init_state, rotation_matrix, target_position)
        # q_goal = np.array([0,0.3,0,-2.43,0,3.54,0,0,0])
        end_time = time.time()
        self.get_logger().info(f"IK Solution time: {end_time - start_time:.2f} seconds")
        self.get_logger().info(f"IK Solution: {q_goal}")
        self.get_logger().info(f"q_start: {q_start}")
        while True:
            # Search for a path
            options = RRTPlannerOptions(
                max_step_size=0.05,
                max_connection_dist=0.1,
                rrt_connect=True,
                bidirectional_rrt=False,
                rrt_star=True,
                max_rewire_dist=0.5,
                max_planning_time=5.0,
                fast_return=True,
                goal_biasing_probability=0.25,
                collision_distance_padding=0.0001,
                verbose=False,
            )
            self.get_logger().info("\nSTEP2:Start Path Planning...")
            planner = RRTPlanner(self.model_roboplan, self.collision_model, options=options)

            q_path = planner.plan(q_start, q_goal)
            # q_path = np.vstack((q_start, q_goal))
            if q_path is not None and len(q_path) > 0:
                self.get_logger().info(f"Got a path with {len(q_path)} waypoints")
                if len(q_path) > 100:
                    self.get_logger().warn("Path is too long, skipping...")
                    continue
            else:
                self.get_logger().error("Failed to find a path.")
                continue
            
            dt = 0.025
            options = CubicTrajectoryOptimizationOptions(
                num_waypoints=len(q_path),
                samples_per_segment=1,
                min_segment_time=0.5,
                max_segment_time=10.0,
                min_vel=-1.5,
                max_vel=1.5,
                min_accel=-0.75,
                max_accel=0.75,
                min_jerk=-1.0,
                max_jerk=1.0,
                max_planning_time=1.0,
                check_collisions=False,
                min_collision_dist=0.001,
                collision_influence_dist=0.05,
                collision_avoidance_cost_weight=0.0,
                collision_link_list=[
                    "ground_plane",
                    "plug"
                ],
            )
            self.get_logger().info("STEP3:Start Trajectory Optimization...")
            optimizer = CubicTrajectoryOptimization(self.model_roboplan, self.collision_model, options)
            traj = optimizer.plan([q_path[0], q_path[-1]], init_path=q_path)

            if traj is None:
            # if True:
                self.get_logger().warn("Trajectory optimization failed, retrying with RRT waypoints...")
                traj = optimizer.plan(q_path, init_path=q_path)

            if traj is not None:
                traj_gen = traj.generate(dt)
                self.q_vec = traj_gen[1]
                self.get_logger().info(f"Trajectory generated with {self.q_vec.shape[1]} points")
                
                # self.publish_trajectory(traj_gen)  # Publish the generated trajectory
                # self.start_plot_thread(q_path)
                self.last_traj_gen = traj_gen
                return traj_gen
            
    def getIk(self, init_state, rotation_matrix, pose):
        solutions = []
        while len(solutions) < 5:
            # 第一次使用起点作为初始状态，之后每次迭代都随机选择一个新的初始状态
            if len(solutions) > 0:
                init_state = self.random_valid_state()
                
            target_tform = pinocchio.SE3(rotation_matrix, np.array(pose))
            q_sol = self.ik.solve(
                self.target_frame,
                target_tform,
                init_state=init_state,
                # nullspace_components=self.nullspace_components,
                nullspace_components=None,
                verbose=False,
            )
            if q_sol is not None:
                # print("IK solution found")
                self.get_logger().info(f"joint movement: {np.sum(np.abs(q_sol - self.q_start)):.4f}")
                solutions.append(q_sol)
        
        # 计算每组解与 q_start 的差值，并找出每组解的最大关节变化量
        max_joint_differences = [np.max(np.abs(q - self.q_start)) for q in solutions]
        
        # 找出最大关节变化量最小的解的索引
        best_idx = np.argmin(max_joint_differences)
        
        # 返回最佳解
        return solutions[best_idx]

    def load_models(self,urdf_filepath,model_dir):
        """
        Gets the example Panda models.

        Returns
        -------
            tuple[`pinocchio.Model`]
                A 3-tuple containing the model, collision geometry model, and visual geometry model.
        """
        return pinocchio.buildModelsFromUrdf(urdf_filepath, package_dirs=model_dir)
    
    def add_self_collisions(self, model, collision_model, srdf_filepath):
        """
        Adds link self-collisions to the Panda collision model.

        This uses an SRDF file to remove any excluded collision pairs.

        Parameters
        ----------
            model : `pinocchio.Model`
                The Panda model.
            collision_model : `pinocchio.Model`
                The Panda collision geometry model.
            srdf_filename : str, optional
                Path to the SRDF file describing the excluded collision pairs.
                If not specified, uses a default file included with the Panda model.
        """
        collision_model.addAllCollisionPairs()
        pinocchio.removeCollisionPairs(model, collision_model, srdf_filepath)

    def add_object_collisions(self, model, collision_model, visual_model, inflation_radius=0.0):
        """
        Adds obstacles and collisions to the Panda collision model.

        Parameters
        ----------
            model : `pinocchio.Model`
                The Panda model.
            collision_model : `pinocchio.Model`
                The Panda collision geometry model.
            visual_model : `pinocchio.Model`
                The Panda visual geometry model.
            inflation_radius : float, optional
                An inflation radius, in meters, around the objects.
        """
        # Add the collision objects
        ground_plane = pinocchio.GeometryObject(
            "ground_plane",
            0,
            pinocchio.SE3(np.eye(3), np.array([0.0, 0.0, -0.151])),
            coal.Box(2.0, 2.0, 0.3),
        )
        ground_plane.meshColor = np.array([0.5, 0.5, 0.5, 0.5])
        visual_model.addGeometryObject(ground_plane)
        collision_model.addGeometryObject(ground_plane)

        collision_names = [
            cobj.name for cobj in collision_model.geometryObjects if "panda" in cobj.name
        ]
        obstacle_names = [
            "ground_plane"
        ]
        for obstacle_name in obstacle_names:
            for collision_name in collision_names:
                set_collisions(model, collision_model, obstacle_name, collision_name, True)

        # Exclude the collision between the ground and the base link
        set_collisions(model, collision_model, "panda_link0", "ground_plane", False)

    def get_valid_init_state(self,q_start):
        if check_collisions_at_state(
            model=self.model_roboplan, 
            collision_model=self.collision_model,
            data=self.data,
            collision_data=None,
            q=q_start,
            distance_padding=0.001
        ):
            return self.random_valid_state()
        else:
            return q_start
        
    def random_valid_state(self):
        return get_random_collision_free_state(
            self.model_roboplan, self.collision_model, distance_padding=0.001
        )

    def plot_trajectories(self, q_path):
        """
        在单独的线程中绘制轨迹。
        """
        
        # 创建一个图形窗口
        plt.ion()  # 启用交互模式
        fig = plt.figure(figsize=(20, 10))  # 设置图形大小

        # 绘制笛卡尔空间轨迹（修改后的部分）
        ax_cart = fig.add_subplot(121, projection='3d')  # 左侧子图，笛卡尔空间
        for link in ["panda_hand", "panda_link7", "panda_link6", "panda_link5", "panda_link4", "panda_link3", "panda_link2", "panda_link1"]:
            tforms_path = extract_cartesian_poses(self.model_roboplan, link, np.array(q_path))
            # 路径点使用'x'标记和红色
            ax_cart = self.plot(tforms_path, ax=ax_cart, label=f"{link} (path)", marker='x', markersize=10)  # 优化前路径点
            tforms_traj = extract_cartesian_poses(self.model_roboplan, link, self.q_vec.T)
            # 轨迹点使用'o'标记和蓝色
            ax_cart = self.plot(tforms_traj, ax=ax_cart, label=f"{link} (trajectory)", marker='o', markersize=5)  # 优化后轨迹
        ax_cart.set_title("Cartesian Space Trajectories")
        ax_cart.legend()  # 确保添加图例

        # 绘制关节空间轨迹（新增部分，包含插值处理）
        ax_joint = fig.add_subplot(122)  # 右侧子图，关节空间
        num_joints = self.q_vec.shape[0]
        traj_time_steps = np.arange(self.q_vec.shape[1]) * 0.025  # 轨迹的时间步长，假设为0.025秒
        path_time_steps = np.linspace(0, traj_time_steps[-1], len(np.array(q_path)))  # 为q_path生成对应的时间步长

        # 绘制优化后的轨迹和优化前的路径（插值后）
        for j in range(num_joints-2):
            ax_joint.plot(traj_time_steps, self.q_vec[j, :], label=f"Joint {j+1} (trajectory)", marker='o', markersize=5)
            ax_joint.plot(path_time_steps, np.array(q_path)[:, j], 'x', label=f"Joint {j+1} (path)", marker='x', markersize=10)

        
        ax_joint.set_xlabel('Time (s)')
        ax_joint.set_ylabel('Joint Angle (rad)')
        ax_joint.set_title("Joint Space Trajectories")
        ax_joint.legend()

        # 显示图形
        plt.tight_layout()
        plt.show(block=False)
        while plt.get_fignums():
            plt.pause(1)

    def start_plot_thread(self, q_path):
        """
        启动绘图线程。
        """
        if self.plot_thread is None or not self.plot_thread.is_alive():
            self.plot_thread = threading.Thread(target=self.plot_trajectories(q_path))
            self.plot_thread.start()

    def plot(self, tfs, ax=None, color=None, label=None, marker='o', markersize=5):
        """
        绘制轨迹到同一个图中。
        如果 ax 为 None,则创建新的图形;否则在传入的 ax 上绘制.
        """
        positions = []
        for tform in tfs:
            position = tform.translation
            positions.append(position)
        positions = np.array(positions)
         # 如果没有传入 ax，则创建新的图形
        if ax is None:
            fig = plt.figure()
            ax = fig.add_subplot(111, projection='3d')
        # 绘制轨迹
        if color is None:
            ax.plot(positions[:, 0], positions[:, 1], positions[:, 2], marker=marker, markersize = markersize, label=label)
        else:
            ax.plot(positions[:, 0], positions[:, 1], positions[:, 2], marker=marker, markersize = markersize, color=color, label=label)
       
        # 设置坐标轴标签
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_zlabel('Z')

        # 添加图例
        ax.legend()
        # plt.show(block=False)
        # plt.pause(0.01)
        
        return ax  # 返回 ax 对象以便后续绘制
            
    def run_mujoco(self, traj_gen):
        """
        Simulate the robot's motion in MuJoCo based on the trajectory.
    
        Parameters
        ----------
        traj_gen : tuple
            A tuple containing (time_points, q_vec), where:
            - time_points: A list or array of time points corresponding to each q_vec column.
            - q_vec: A 2D array of joint positions (shape: [num_joints, num_points]).
        """
        print("\nRunning MuJoCo simulation...")
        if traj_gen is None:
            self.get_logger().error("No trajectory generated. Cannot run simulation.")
            return
            
        # 使用从参数中获取的路径和时间步长
        mjcf_filepath = os.path.join(self.model_dir, "panda_description", "mjcf", "scene.xml")
        model = mujoco.MjModel.from_xml_path(mjcf_filepath)
        model.opt.timestep = self.dt
        data = mujoco.MjData(model)
        viewer = mujoco_viewer.MujocoViewer(model, data)
    
        # Unpack trajectory into time points and joint positions
        time_points= traj_gen[0]
        q_vec = traj_gen[1]
        num_points = q_vec.shape[1]
        print(f"num_points: {num_points}")
    
        for idx in range(num_points - 1):
            # Set joint positions for the current step
            data.qpos[:7] = q_vec[:7, idx]
            mujoco.mj_forward(model, data)  # Ensure the state is updated
    
            # Calculate the time interval to the next point
            time_interval = time_points[idx + 1] - time_points[idx]
            steps = int(time_interval / self.dt)  # Number of simulation steps for this interval
    
            # Simulate for the calculated number of steps
            for _ in range(steps):
                if _ == 0:
                    mujoco.mj_step(model, data)
                viewer.render()
        
        while viewer.is_alive:
        # while rclpy.ok() and viewer.is_alive:
            # rclpy.spin_once(self, timeout_sec=0.01)
            viewer.render()
            
        viewer.close()
        
def main():
    rclpy.init()
    planner = MotionPlanning()
    # rate = Node.create_rate(planner, 10)
    planned_flag = False
    try:
        while rclpy.ok():
            # 处理一次回调队列
            rclpy.spin_once(planner, timeout_sec=1)
            # planner.get_logger().info("Node is running...")
            if not planned_flag:
                traj_gen = planner.planning()
                if traj_gen is not None:
                    planner.last_traj_gen = traj_gen
                    planned_flag = True
                    # 如果需要运行MuJoCo仿真，可以取消注释下面的行
                    # planner.run_mujoco(traj_gen)
            
    finally:
        planner.destroy_node()
        rclpy.shutdown()
    
if __name__ == '__main__':
    main()