import rclpy
from rclpy.node import Node
from geometry_msgs.msg import PoseStamped, TransformStamped
from trajectory_msgs.msg import JointTrajectory, JointTrajectoryPoint
from tf2_ros import TransformBroadcaster
from sensor_msgs.msg import JointState
import mujoco,mujoco_viewer
import numpy as np
import pinocchio
import coal
import os
import threading
import matplotlib.pyplot as plt
import time
# matplotlib.use('TkAgg')
from .utils import (
    set_collisions,
    check_collisions_at_state,
    get_random_collision_free_state,
    extract_cartesian_poses
)
from .nullspace_components import (
    joint_limit_nullspace_component,
    collision_avoidance_nullspace_component
)
# from .differential_ik import DifferentialIkOptions, DifferentialIk
# from .rrt import RRTPlanner, RRTPlannerOptions
# from .trajectory_optimization import CubicTrajectoryOptimizationOptions, CubicTrajectoryOptimization
# from scipy.spatial.transform import Rotation as R
# from ament_index_python.packages import get_package_share_directory



class MotionPlanning(Node):
    def __init__(self):
        super().__init__('forward_fk_node')
        
        # 声明参数，从launch文件中读取
        self.declare_parameter('dt', 0.001)
        self.declare_parameter('model_dir', '/home/<USER>/ccag/ccag_ws/src/mujoco_sim/mujoco_sim/descriptions/')
        # 获取参数值
        self.dt = self.get_parameter('dt').get_parameter_value().double_value
        self.model_dir = self.get_parameter('model_dir').get_parameter_value().string_value
        
        self.init_planning()
        # Subscriber for joint states
        self.joint_state_sub = self.create_subscription(
            JointState,
            '/joint_states',
            self.joint_state_callback,
            10
        )    
        # TF broadcaster
        self.tf_broadcaster = TransformBroadcaster(self)

        self.tf_timer = self.create_timer(0.01, self.publish_tf)  # 100Hz

    def joint_state_callback(self, msg):
        """
        Callback function to handle received joint states.
        Extracts joint positions and computes forward kinematics.

        Parameters
        ----------
        msg : JointState
            The received joint state message.
        """
        # Extract joint positions (first 7 joints for arm)
        self.current_joint_state = msg
        if len(msg.position) >= 7:  # Ensure we have enough joints
            self.current_joint_positions = np.array(msg.position[:7], dtype=np.float64)

    def publish_tf(self):
        """
        Publish the current end-effector TF at 100Hz using forward kinematics.
        Uses actual joint angles from /joint_states topic.
        """
        if hasattr(self, 'model_roboplan') and hasattr(self, 'data'):
            # Get current joint angles from joint state
            if hasattr(self, 'current_joint_positions'):
                q_cur = np.zeros(self.model_roboplan.nq)
                q_cur[:7] = self.current_joint_positions  # Only use first 7 joints for arm
            else:
                q_cur = np.zeros(self.model_roboplan.nq)
            
            # Compute and publish TF
            tf_msg = self.create_tf_message(q_cur)
            self.tf_broadcaster.sendTransform(tf_msg)

    def create_tf_message(self,q_cur):
        """
        Create a TF message using pinocchio forward kinematics.
        
        Parameters
        ----------
        q_cur : numpy.ndarray
            Current joint angles.
            
        Returns
        -------
        TransformStamped
            The TF message.
        """
        # Compute forward kinematics
        target_frame_id = self.model_roboplan.getFrameId("plug")
        pinocchio.framesForwardKinematics(self.model_roboplan, self.data, q_cur)
        cur_tform = self.data.oMf[target_frame_id]
        
        # Convert to TF message
        tf_msg = TransformStamped()
        tf_msg.header.stamp = self.get_clock().now().to_msg()
        tf_msg.header.frame_id = "base_link"
        tf_msg.child_frame_id = "plug"
        
        # Set translation from pinocchio transform
        tf_msg.transform.translation.x = cur_tform.translation[0]
        tf_msg.transform.translation.y = cur_tform.translation[1]
        tf_msg.transform.translation.z = cur_tform.translation[2]
        
        # Convert rotation matrix to quaternion
        quat = pinocchio.Quaternion(cur_tform.rotation)
        tf_msg.transform.rotation.x = quat.x
        tf_msg.transform.rotation.y = quat.y
        tf_msg.transform.rotation.z = quat.z
        tf_msg.transform.rotation.w = quat.w
        
        return tf_msg
        
    def init_planning(self):
        urdf_filepath = os.path.join(self.model_dir, "panda_description", "urdf", "panda.urdf")
        srdf_filepath = os.path.join(self.model_dir, "panda_description", "srdf", "panda.srdf")
        self.model_roboplan, self.collision_model, visual_model = self.load_models(urdf_filepath,self.model_dir)
        self.add_self_collisions(self.model_roboplan, self.collision_model, srdf_filepath)
        self.add_object_collisions(self.model_roboplan, self.collision_model, visual_model, inflation_radius=0.1)

        self.data = self.model_roboplan.createData()
        collision_data = self.collision_model.createData()


    def load_models(self,urdf_filepath,model_dir):
        """
        Gets the example Panda models.

        Returns
        -------
            tuple[`pinocchio.Model`]
                A 3-tuple containing the model, collision geometry model, and visual geometry model.
        """
        return pinocchio.buildModelsFromUrdf(urdf_filepath, package_dirs=model_dir)
    
    def add_self_collisions(self, model, collision_model, srdf_filepath):
        """
        Adds link self-collisions to the Panda collision model.

        This uses an SRDF file to remove any excluded collision pairs.

        Parameters
        ----------
            model : `pinocchio.Model`
                The Panda model.
            collision_model : `pinocchio.Model`
                The Panda collision geometry model.
            srdf_filename : str, optional
                Path to the SRDF file describing the excluded collision pairs.
                If not specified, uses a default file included with the Panda model.
        """
        collision_model.addAllCollisionPairs()
        pinocchio.removeCollisionPairs(model, collision_model, srdf_filepath)

    def add_object_collisions(self, model, collision_model, visual_model, inflation_radius=0.0):
        """
        Adds obstacles and collisions to the Panda collision model.

        Parameters
        ----------
            model : `pinocchio.Model`
                The Panda model.
            collision_model : `pinocchio.Model`
                The Panda collision geometry model.
            visual_model : `pinocchio.Model`
                The Panda visual geometry model.
            inflation_radius : float, optional
                An inflation radius, in meters, around the objects.
        """
        # Add the collision objects
        ground_plane = pinocchio.GeometryObject(
            "ground_plane",
            0,
            pinocchio.SE3(np.eye(3), np.array([0.0, 0.0, -0.151])),
            coal.Box(2.0, 2.0, 0.3),
        )
        ground_plane.meshColor = np.array([0.5, 0.5, 0.5, 0.5])
        visual_model.addGeometryObject(ground_plane)
        collision_model.addGeometryObject(ground_plane)

        collision_names = [
            cobj.name for cobj in collision_model.geometryObjects if "panda" in cobj.name
        ]
        obstacle_names = [
            "ground_plane"
        ]
        for obstacle_name in obstacle_names:
            for collision_name in collision_names:
                set_collisions(model, collision_model, obstacle_name, collision_name, True)

        # Exclude the collision between the ground and the base link
        set_collisions(model, collision_model, "panda_link0", "ground_plane", False)
        
def main():
    rclpy.init()
    planner = MotionPlanning()
    rclpy.spin(planner)
    rclpy.shutdown()
    
if __name__ == '__main__':
    main()