{"artifacts": [{"path": "libpose_estimation.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "ament_target_dependencies", "set_target_properties", "include", "find_package", "add_definitions", "include_directories", "target_include_directories"], "files": ["CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppExport.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppConfig.cmake", "/opt/ros/humble/share/rcl/cmake/rclExport.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl/cmake/rclConfig.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/libstatistics_collectorConfig.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserExport.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserConfig.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rclcpp_components/cmake/export_rclcpp_componentsExport.cmake", "/opt/ros/humble/share/rclcpp_components/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp_components/cmake/rclcpp_componentsConfig.cmake", "/opt/ros/humble/share/class_loader/cmake/class_loaderExport.cmake", "/opt/ros/humble/share/class_loader/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/class_loader/cmake/class_loaderConfig.cmake", "/opt/ros/humble/share/rclcpp_components/cmake/ament_cmake_export_dependencies-extras.cmake", "/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake/export_data_interface__rosidl_typesupport_fastrtps_cExport.cmake", "/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake/data_interfaceConfig.cmake", "/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake/export_data_interface__rosidl_typesupport_fastrtps_cppExport.cmake", "/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake/data_interface__rosidl_typesupport_introspection_cExport.cmake", "/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake/data_interface__rosidl_typesupport_introspection_cppExport.cmake", "/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake/data_interface__rosidl_typesupport_cppExport.cmake", "/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake/export_data_interface__rosidl_generator_pyExport.cmake", "/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake/data_interface__rosidl_typesupport_cExport.cmake", "/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake/export_data_interface__rosidl_generator_cExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/builtin_interfacesConfig.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/humble/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 40, "parent": 0}, {"command": 1, "file": 0, "line": 72, "parent": 0}, {"command": 3, "file": 0, "line": 50, "parent": 0}, {"command": 2, "file": 1, "line": 145, "parent": 3}, {"command": 2, "file": 0, "line": 59, "parent": 0}, {"command": 6, "file": 0, "line": 23, "parent": 0}, {"file": 4, "parent": 6}, {"command": 5, "file": 4, "line": 41, "parent": 7}, {"file": 3, "parent": 8}, {"command": 5, "file": 3, "line": 9, "parent": 9}, {"file": 2, "parent": 10}, {"command": 4, "file": 2, "line": 56, "parent": 11}, {"command": 5, "file": 4, "line": 41, "parent": 7}, {"file": 10, "parent": 13}, {"command": 6, "file": 10, "line": 21, "parent": 14}, {"file": 9, "parent": 15}, {"command": 5, "file": 9, "line": 41, "parent": 16}, {"file": 8, "parent": 17}, {"command": 6, "file": 8, "line": 21, "parent": 18}, {"file": 7, "parent": 19}, {"command": 5, "file": 7, "line": 41, "parent": 20}, {"file": 6, "parent": 21}, {"command": 5, "file": 6, "line": 9, "parent": 22}, {"file": 5, "parent": 23}, {"command": 4, "file": 5, "line": 56, "parent": 24}, {"command": 5, "file": 7, "line": 41, "parent": 20}, {"file": 14, "parent": 26}, {"command": 6, "file": 14, "line": 21, "parent": 27}, {"file": 13, "parent": 28}, {"command": 5, "file": 13, "line": 41, "parent": 29}, {"file": 12, "parent": 30}, {"command": 5, "file": 12, "line": 9, "parent": 31}, {"file": 11, "parent": 32}, {"command": 4, "file": 11, "line": 56, "parent": 33}, {"command": 6, "file": 0, "line": 24, "parent": 0}, {"file": 17, "parent": 35}, {"command": 5, "file": 17, "line": 41, "parent": 36}, {"file": 16, "parent": 37}, {"command": 5, "file": 16, "line": 9, "parent": 38}, {"file": 15, "parent": 39}, {"command": 4, "file": 15, "line": 56, "parent": 40}, {"command": 5, "file": 17, "line": 41, "parent": 36}, {"file": 21, "parent": 42}, {"command": 6, "file": 21, "line": 21, "parent": 43}, {"file": 20, "parent": 44}, {"command": 5, "file": 20, "line": 41, "parent": 45}, {"file": 19, "parent": 46}, {"command": 5, "file": 19, "line": 9, "parent": 47}, {"file": 18, "parent": 48}, {"command": 4, "file": 18, "line": 56, "parent": 49}, {"command": 4, "file": 15, "line": 64, "parent": 40}, {"command": 6, "file": 0, "line": 25, "parent": 0}, {"file": 24, "parent": 52}, {"command": 5, "file": 24, "line": 41, "parent": 53}, {"file": 23, "parent": 54}, {"command": 5, "file": 23, "line": 9, "parent": 55}, {"file": 22, "parent": 56}, {"command": 4, "file": 22, "line": 56, "parent": 57}, {"command": 5, "file": 23, "line": 9, "parent": 55}, {"file": 25, "parent": 59}, {"command": 4, "file": 25, "line": 56, "parent": 60}, {"command": 5, "file": 23, "line": 9, "parent": 55}, {"file": 26, "parent": 62}, {"command": 4, "file": 26, "line": 56, "parent": 63}, {"command": 5, "file": 23, "line": 9, "parent": 55}, {"file": 27, "parent": 65}, {"command": 4, "file": 27, "line": 56, "parent": 66}, {"command": 5, "file": 23, "line": 9, "parent": 55}, {"file": 28, "parent": 68}, {"command": 4, "file": 28, "line": 56, "parent": 69}, {"command": 5, "file": 23, "line": 9, "parent": 55}, {"file": 29, "parent": 71}, {"command": 4, "file": 29, "line": 56, "parent": 72}, {"command": 5, "file": 23, "line": 9, "parent": 55}, {"file": 30, "parent": 74}, {"command": 4, "file": 30, "line": 56, "parent": 75}, {"command": 5, "file": 23, "line": 9, "parent": 55}, {"file": 31, "parent": 77}, {"command": 4, "file": 31, "line": 56, "parent": 78}, {"command": 6, "file": 8, "line": 21, "parent": 18}, {"file": 36, "parent": 80}, {"command": 5, "file": 36, "line": 41, "parent": 81}, {"file": 35, "parent": 82}, {"command": 6, "file": 35, "line": 21, "parent": 83}, {"file": 34, "parent": 84}, {"command": 5, "file": 34, "line": 41, "parent": 85}, {"file": 33, "parent": 86}, {"command": 5, "file": 33, "line": 9, "parent": 87}, {"file": 32, "parent": 88}, {"command": 4, "file": 32, "line": 56, "parent": 89}, {"command": 6, "file": 35, "line": 21, "parent": 83}, {"file": 41, "parent": 91}, {"command": 5, "file": 41, "line": 41, "parent": 92}, {"file": 40, "parent": 93}, {"command": 6, "file": 40, "line": 21, "parent": 94}, {"file": 39, "parent": 95}, {"command": 5, "file": 39, "line": 41, "parent": 96}, {"file": 38, "parent": 97}, {"command": 5, "file": 38, "line": 9, "parent": 98}, {"file": 37, "parent": 99}, {"command": 4, "file": 37, "line": 56, "parent": 100}, {"command": 7, "file": 0, "line": 13, "parent": 0}, {"command": 8, "file": 0, "line": 31, "parent": 0}, {"command": 9, "file": 0, "line": 44, "parent": 0}, {"command": 9, "file": 1, "line": 141, "parent": 3}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -O2 -msse4 -O3 -DNDEBUG -std=gnu++17 -fPIC"}], "defines": [{"backtrace": 4, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 102, "define": "ENABLE_SSE"}, {"backtrace": 4, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"define": "pose_estimation_EXPORTS"}], "includes": [{"backtrace": 103, "path": "/home/<USER>/ccag/ccag_ws/src/pose_estimation/include"}, {"backtrace": 103, "path": "/usr/local/include/eigen3"}, {"backtrace": 104, "path": "/home/<USER>/ccag/ccag_ws/src/pose_estimation/include/pose_estimation"}, {"backtrace": 103, "isSystem": true, "path": "/usr/include/opencv4"}, {"backtrace": 105, "isSystem": true, "path": "/home/<USER>/ccag/ccag_ws/install/data_interface/include/data_interface"}, {"backtrace": 105, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp"}, {"backtrace": 105, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_components"}, {"backtrace": 105, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 105, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/ament_index_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/libstatistics_collector"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_logging_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_yaml_param_parser"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/libyaml_vendor"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tracetools"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcpputils"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/statistics_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosgraph_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/class_loader"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/composition_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}], "language": "CXX", "languageStandard": {"backtraces": [4, 5], "standard": "17"}, "sourceIndexes": [0]}], "id": "pose_estimation::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib"}, {"backtrace": 2, "path": "lib"}], "prefix": {"path": "/home/<USER>/ccag/ccag_ws/install/pose_estimation"}}, "link": {"commandFragments": [{"fragment": "-Wl,-r<PERSON>,/opt/ros/humble/lib:/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/usr/local/lib:", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libcomponent_manager.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/home/<USER>/ccag/ccag_ws/install/data_interface/lib/libdata_interface__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/home/<USER>/ccag/ccag_ws/install/data_interface/lib/libdata_interface__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/home/<USER>/ccag/ccag_ws/install/data_interface/lib/libdata_interface__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/home/<USER>/ccag/ccag_ws/install/data_interface/lib/libdata_interface__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/home/<USER>/ccag/ccag_ws/install/data_interface/lib/libdata_interface__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/home/<USER>/ccag/ccag_ws/install/data_interface/lib/libdata_interface__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/local/lib/libg2o_core.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/local/lib/libg2o_stuff.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/local/lib/libg2o_solver_cholmod.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/local/lib/libg2o_types_slam3d.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_stitching.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_alphamat.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_aruco.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_barcode.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_bgsegm.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_bioinspired.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_ccalib.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dnn_objdetect.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dnn_superres.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dpm.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_face.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_freetype.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_fuzzy.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_hdf.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_hfs.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_img_hash.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_intensity_transform.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_line_descriptor.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_mcc.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_quality.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_rapid.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_reg.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_rgbd.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_saliency.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_shape.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_stereo.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_structured_light.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_superres.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_surface_matching.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_tracking.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_videostab.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_viz.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_wechat_qrcode.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_xobjdetect.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_xphoto.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/liblibstatistics_collector.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librcl.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/opt/ros/humble/lib/librmw_implementation.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/opt/ros/humble/lib/librcl_logging_spdlog.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/opt/ros/humble/lib/librcl_logging_interface.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"backtrace": 34, "fragment": "/opt/ros/humble/lib/libyaml.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libtracetools.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libament_index_cpp.so", "role": "libraries"}, {"backtrace": 41, "fragment": "/opt/ros/humble/lib/libclass_loader.so", "role": "libraries"}, {"backtrace": 50, "fragment": "/usr/lib/x86_64-linux-gnu/libconsole_bridge.so.1.0", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 58, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 58, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 61, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 58, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 58, "fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"backtrace": 61, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 64, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 67, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 67, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 64, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 70, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/home/<USER>/ccag/ccag_ws/install/data_interface/lib/libdata_interface__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/home/<USER>/ccag/ccag_ws/install/data_interface/lib/libdata_interface__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 76, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 79, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 90, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 79, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 101, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 73, "fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_highgui.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_datasets.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_plot.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_text.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_ml.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_phase_unwrapping.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_optflow.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_ximgproc.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_video.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_videoio.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_imgcodecs.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_objdetect.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_calib3d.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dnn.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_features2d.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_flann.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_photo.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_imgproc.so.4.5.4d", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_core.so.4.5.4d", "role": "libraries"}], "language": "CXX"}, "name": "pose_estimation", "nameOnDisk": "libpose_estimation.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/pose_estimation_3d2d.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}