set(_AMENT_PACKAGE_NAME "pose_estimation")
set(pose_estimation_VERSION "0.0.0")
set(pose_estimation_MAINTAINER "jie wang <<EMAIL>>")
set(pose_estimation_BUILD_DEPENDS "pose_estimation_msg_srv" "std_msgs" "geometry_msgs" "rclcpp" "rclcpp_components")
set(pose_estimation_BUILDTOOL_DEPENDS "ament_cmake")
set(pose_estimation_BUILD_EXPORT_DEPENDS "pose_estimation_msg_srv" "std_msgs" "geometry_msgs" "rclcpp" "rclcpp_components")
set(pose_estimation_BUILDTOOL_EXPORT_DEPENDS )
set(pose_estimation_EXEC_DEPENDS "pose_estimation_msg_srv" "std_msgs" "geometry_msgs" "rclcpp" "rclcpp_components")
set(pose_estimation_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(pose_estimation_GROUP_DEPENDS )
set(pose_estimation_MEMBER_OF_GROUPS )
set(pose_estimation_DEPRECATED "")
set(pose_estimation_EXPORT_TAGS)
list(APPEND pose_estimation_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
