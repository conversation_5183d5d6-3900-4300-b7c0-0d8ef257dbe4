{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-d407313bc865c5ee195b.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "pose_estimation_client", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "pose_estimation_client::@6890427a1f51a3e7e1df", "jsonFile": "target-pose_estimation_client-eebf77bba65a3b14e674.json", "name": "pose_estimation_client", "projectIndex": 0}, {"directoryIndex": 0, "id": "pose_estimation_client_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-pose_estimation_client_uninstall-a1bf6d8b01fe5d601b02.json", "name": "pose_estimation_client_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-d0d1df3f7fd69d8d02c9.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/ccag/ccag_ws/build/pose_estimation_client", "source": "/home/<USER>/ccag/ccag_ws/src/pose_estimation_client"}, "version": {"major": 2, "minor": 8}}