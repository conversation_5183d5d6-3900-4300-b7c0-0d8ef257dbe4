{"artifacts": [{"path": "pose_estimation_client"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "ament_target_dependencies", "set_target_properties", "include", "find_package", "add_compile_options", "target_include_directories"], "files": ["CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppExport.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppConfig.cmake", "/opt/ros/humble/share/rcl/cmake/rclExport.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl/cmake/rclConfig.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/libstatistics_collectorConfig.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserExport.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserConfig.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_dependencies-extras.cmake", "/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake/export_data_interface__rosidl_typesupport_fastrtps_cExport.cmake", "/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake/data_interfaceConfig.cmake", "/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake/export_data_interface__rosidl_typesupport_fastrtps_cppExport.cmake", "/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake/data_interface__rosidl_typesupport_introspection_cExport.cmake", "/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake/data_interface__rosidl_typesupport_introspection_cppExport.cmake", "/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake/data_interface__rosidl_typesupport_cppExport.cmake", "/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake/export_data_interface__rosidl_generator_pyExport.cmake", "/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake/data_interface__rosidl_typesupport_cExport.cmake", "/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake/export_data_interface__rosidl_generator_cExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/builtin_interfacesConfig.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/humble/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 19, "parent": 0}, {"command": 1, "file": 0, "line": 28, "parent": 0}, {"command": 3, "file": 0, "line": 22, "parent": 0}, {"command": 2, "file": 1, "line": 145, "parent": 3}, {"command": 6, "file": 0, "line": 15, "parent": 0}, {"file": 4, "parent": 5}, {"command": 5, "file": 4, "line": 41, "parent": 6}, {"file": 3, "parent": 7}, {"command": 5, "file": 3, "line": 9, "parent": 8}, {"file": 2, "parent": 9}, {"command": 4, "file": 2, "line": 56, "parent": 10}, {"command": 5, "file": 4, "line": 41, "parent": 6}, {"file": 10, "parent": 12}, {"command": 6, "file": 10, "line": 21, "parent": 13}, {"file": 9, "parent": 14}, {"command": 5, "file": 9, "line": 41, "parent": 15}, {"file": 8, "parent": 16}, {"command": 6, "file": 8, "line": 21, "parent": 17}, {"file": 7, "parent": 18}, {"command": 5, "file": 7, "line": 41, "parent": 19}, {"file": 6, "parent": 20}, {"command": 5, "file": 6, "line": 9, "parent": 21}, {"file": 5, "parent": 22}, {"command": 4, "file": 5, "line": 56, "parent": 23}, {"command": 5, "file": 7, "line": 41, "parent": 19}, {"file": 14, "parent": 25}, {"command": 6, "file": 14, "line": 21, "parent": 26}, {"file": 13, "parent": 27}, {"command": 5, "file": 13, "line": 41, "parent": 28}, {"file": 12, "parent": 29}, {"command": 5, "file": 12, "line": 9, "parent": 30}, {"file": 11, "parent": 31}, {"command": 4, "file": 11, "line": 56, "parent": 32}, {"command": 6, "file": 0, "line": 16, "parent": 0}, {"file": 17, "parent": 34}, {"command": 5, "file": 17, "line": 41, "parent": 35}, {"file": 16, "parent": 36}, {"command": 5, "file": 16, "line": 9, "parent": 37}, {"file": 15, "parent": 38}, {"command": 4, "file": 15, "line": 56, "parent": 39}, {"command": 5, "file": 16, "line": 9, "parent": 37}, {"file": 18, "parent": 41}, {"command": 4, "file": 18, "line": 56, "parent": 42}, {"command": 5, "file": 16, "line": 9, "parent": 37}, {"file": 19, "parent": 44}, {"command": 4, "file": 19, "line": 56, "parent": 45}, {"command": 5, "file": 16, "line": 9, "parent": 37}, {"file": 20, "parent": 47}, {"command": 4, "file": 20, "line": 56, "parent": 48}, {"command": 5, "file": 16, "line": 9, "parent": 37}, {"file": 21, "parent": 50}, {"command": 4, "file": 21, "line": 56, "parent": 51}, {"command": 5, "file": 16, "line": 9, "parent": 37}, {"file": 22, "parent": 53}, {"command": 4, "file": 22, "line": 56, "parent": 54}, {"command": 5, "file": 16, "line": 9, "parent": 37}, {"file": 23, "parent": 56}, {"command": 4, "file": 23, "line": 56, "parent": 57}, {"command": 5, "file": 16, "line": 9, "parent": 37}, {"file": 24, "parent": 59}, {"command": 4, "file": 24, "line": 56, "parent": 60}, {"command": 6, "file": 8, "line": 21, "parent": 17}, {"file": 29, "parent": 62}, {"command": 5, "file": 29, "line": 41, "parent": 63}, {"file": 28, "parent": 64}, {"command": 6, "file": 28, "line": 21, "parent": 65}, {"file": 27, "parent": 66}, {"command": 5, "file": 27, "line": 41, "parent": 67}, {"file": 26, "parent": 68}, {"command": 5, "file": 26, "line": 9, "parent": 69}, {"file": 25, "parent": 70}, {"command": 4, "file": 25, "line": 56, "parent": 71}, {"command": 6, "file": 28, "line": 21, "parent": 65}, {"file": 34, "parent": 73}, {"command": 5, "file": 34, "line": 41, "parent": 74}, {"file": 33, "parent": 75}, {"command": 6, "file": 33, "line": 21, "parent": 76}, {"file": 32, "parent": 77}, {"command": 5, "file": 32, "line": 41, "parent": 78}, {"file": 31, "parent": 79}, {"command": 5, "file": 31, "line": 9, "parent": 80}, {"file": 30, "parent": 81}, {"command": 4, "file": 30, "line": 56, "parent": 82}, {"command": 7, "file": 0, "line": 10, "parent": 0}, {"command": 8, "file": 1, "line": 141, "parent": 3}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-std=gnu++17"}, {"backtrace": 84, "fragment": "-Wall"}, {"backtrace": 84, "fragment": "-Wextra"}, {"backtrace": 84, "fragment": "-Wpedantic"}], "defines": [{"backtrace": 4, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 4, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}], "includes": [{"backtrace": 85, "isSystem": true, "path": "/home/<USER>/ccag/ccag_ws/install/data_interface/include/data_interface"}, {"backtrace": 85, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/ament_index_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/libstatistics_collector"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_logging_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_yaml_param_parser"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/libyaml_vendor"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tracetools"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcpputils"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/statistics_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosgraph_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}], "language": "CXX", "languageStandard": {"backtraces": [4], "standard": "17"}, "sourceIndexes": [0]}], "id": "pose_estimation_client::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib/pose_estimation_client"}], "prefix": {"path": "/home/<USER>/ccag/ccag_ws/install/pose_estimation_client"}}, "link": {"commandFragments": [{"fragment": "-Wl,-rpath,/opt/ros/humble/lib:/home/<USER>/ccag/ccag_ws/install/data_interface/lib:", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/home/<USER>/ccag/ccag_ws/install/data_interface/lib/libdata_interface__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/home/<USER>/ccag/ccag_ws/install/data_interface/lib/libdata_interface__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/home/<USER>/ccag/ccag_ws/install/data_interface/lib/libdata_interface__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/home/<USER>/ccag/ccag_ws/install/data_interface/lib/libdata_interface__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/home/<USER>/ccag/ccag_ws/install/data_interface/lib/libdata_interface__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/home/<USER>/ccag/ccag_ws/install/data_interface/lib/libdata_interface__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/liblibstatistics_collector.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcl.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librmw_implementation.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libament_index_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librcl_logging_spdlog.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librcl_logging_interface.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libyaml.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libtracetools.so", "role": "libraries"}, {"backtrace": 40, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 40, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 40, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 40, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 40, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 40, "fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 46, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 46, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 46, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 49, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 49, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 49, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 49, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 46, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/home/<USER>/ccag/ccag_ws/install/data_interface/lib/libdata_interface__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/home/<USER>/ccag/ccag_ws/install/data_interface/lib/libdata_interface__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 55, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 58, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 61, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 55, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 58, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 61, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 55, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 58, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 61, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 72, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 61, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 83, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 55, "fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}], "language": "CXX"}, "name": "pose_estimation_client", "nameOnDisk": "pose_estimation_client", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/pose_estimation_client.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}