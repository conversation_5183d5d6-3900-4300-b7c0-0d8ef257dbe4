set(_AMENT_PACKAGE_NAME "pose_estimation_client")
set(pose_estimation_client_VERSION "0.0.0")
set(pose_estimation_client_MAINTAINER "Ji<PERSON> <<EMAIL>>")
set(pose_estimation_client_BUILD_DEPENDS "rclcpp" "pose_estimation_msg_srv" "std_msgs")
set(pose_estimation_client_BUILDTOOL_DEPENDS "ament_cmake")
set(pose_estimation_client_BUILD_EXPORT_DEPENDS "rclcpp" "pose_estimation_msg_srv" "std_msgs")
set(pose_estimation_client_BUILDTOOL_EXPORT_DEPENDS )
set(pose_estimation_client_EXEC_DEPENDS "rclcpp" "pose_estimation_msg_srv" "std_msgs")
set(pose_estimation_client_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(pose_estimation_client_GROUP_DEPENDS )
set(pose_estimation_client_MEMBER_OF_GROUPS )
set(pose_estimation_client_DEPRECATED "")
set(pose_estimation_client_EXPORT_TAGS)
list(APPEND pose_estimation_client_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
