{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-8c0d52c757309e817003.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "detected_post_processing", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "detected_post_processing_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-detected_post_processing_uninstall-9803a5501c68fbeba804.json", "name": "detected_post_processing_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "post_processing::@6890427a1f51a3e7e1df", "jsonFile": "target-post_processing-ea20a2dc4bb294f00b35.json", "name": "post_processing", "projectIndex": 0}, {"directoryIndex": 0, "id": "post_processing_test::@6890427a1f51a3e7e1df", "jsonFile": "target-post_processing_test-de6d86d682501168e38a.json", "name": "post_processing_test", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-9460db94fd21507b37a4.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/ccag/ccag_ws/build/detected_post_processing", "source": "/home/<USER>/ccag/ccag_ws/src/detected_post_processing"}, "version": {"major": 2, "minor": 3}}