set(_AMENT_PACKAGE_NAME "detected_post_processing")
set(detected_post_processing_VERSION "0.0.0")
set(detected_post_processing_MAINTAINER "jsy <<EMAIL>>")
set(detected_post_processing_BUILD_DEPENDS "rclcpp" "sensor_msgs" "vision_msgs" "geometry_msgs" "cv_bridge" "message_filters" "tf2" "tf2_ros" "tf2_geometry_msgs" "data_interface")
set(detected_post_processing_BUILDTOOL_DEPENDS "ament_cmake")
set(detected_post_processing_BUILD_EXPORT_DEPENDS "rclcpp" "sensor_msgs" "vision_msgs" "geometry_msgs" "cv_bridge" "message_filters" "tf2" "tf2_ros" "tf2_geometry_msgs" "data_interface")
set(detected_post_processing_BUILDTOOL_EXPORT_DEPENDS )
set(detected_post_processing_EXEC_DEPENDS "rclcpp" "sensor_msgs" "vision_msgs" "geometry_msgs" "cv_bridge" "message_filters" "tf2" "tf2_ros" "tf2_geometry_msgs" "data_interface")
set(detected_post_processing_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(detected_post_processing_GROUP_DEPENDS )
set(detected_post_processing_MEMBER_OF_GROUPS )
set(detected_post_processing_DEPRECATED "")
set(detected_post_processing_EXPORT_TAGS)
list(APPEND detected_post_processing_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
