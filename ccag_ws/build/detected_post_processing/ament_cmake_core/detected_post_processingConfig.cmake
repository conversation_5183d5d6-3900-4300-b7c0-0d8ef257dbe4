# generated from ament/cmake/core/templates/nameConfig.cmake.in

# prevent multiple inclusion
if(_detected_post_processing_CONFIG_INCLUDED)
  # ensure to keep the found flag the same
  if(NOT DEFINED detected_post_processing_FOUND)
    # explicitly set it to FALSE, otherwise <PERSON><PERSON><PERSON> will set it to TRUE
    set(detected_post_processing_FOUND FALSE)
  elseif(NOT detected_post_processing_FOUND)
    # use separate condition to avoid uninitialized variable warning
    set(detected_post_processing_FOUND FALSE)
  endif()
  return()
endif()
set(_detected_post_processing_CONFIG_INCLUDED TRUE)

# output package information
if(NOT detected_post_processing_FIND_QUIETLY)
  message(STATUS "Found detected_post_processing: 0.0.0 (${detected_post_processing_DIR})")
endif()

# warn when using a deprecated package
if(NOT "" STREQUAL "")
  set(_msg "Package 'detected_post_processing' is deprecated")
  # append custom deprecation text if available
  if(NOT "" STREQUAL "TRUE")
    set(_msg "${_msg} ()")
  endif()
  # optionally quiet the deprecation message
  if(NOT ${detected_post_processing_DEPRECATED_QUIET})
    message(DEPRECATION "${_msg}")
  endif()
endif()

# flag package as ament-based to distinguish it after being find_package()-ed
set(detected_post_processing_FOUND_AMENT_PACKAGE TRUE)

# include all config extra files
set(_extras "")
foreach(_extra ${_extras})
  include("${detected_post_processing_DIR}/${_extra}")
endforeach()
