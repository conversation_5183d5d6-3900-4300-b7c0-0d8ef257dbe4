{"package_name": "data_interface", "output_dir": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_typesupport_fastrtps_c/data_interface", "template_dir": "/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/resource", "idl_tuples": ["/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_adapter/data_interface:msg/PoseEst3d2dPointMsg.idl", "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_adapter/data_interface:msg/PoseEst3d2dPointsMsg.idl", "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_adapter/data_interface:srv/PoseEst3d2dSrv.idl", "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_adapter/data_interface:srv/CameraImage.idl", "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_adapter/data_interface:srv/InitPandaRobot.idl"], "ros_interface_dependencies": ["sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/BatteryState.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/CameraInfo.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/ChannelFloat32.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/CompressedImage.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/FluidPressure.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/Illuminance.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/Image.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/Imu.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/JointState.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/Joy.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/JoyFeedback.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/JoyFeedbackArray.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/LaserEcho.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/LaserScan.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/MagneticField.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/MultiDOFJointState.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/MultiEchoLaserScan.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/NavSatFix.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/NavSatStatus.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/PointCloud.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/PointCloud2.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/PointField.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/Range.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/RegionOfInterest.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/RelativeHumidity.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/Temperature.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/msg/TimeReference.idl", "sensor_msgs:/opt/ros/humble/share/sensor_msgs/srv/SetCameraInfo.idl", "builtin_interfaces:/opt/ros/humble/share/builtin_interfaces/msg/Duration.idl", "builtin_interfaces:/opt/ros/humble/share/builtin_interfaces/msg/Time.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/Accel.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/AccelStamped.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/AccelWithCovariance.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/AccelWithCovarianceStamped.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/Inertia.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/InertiaStamped.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/Point.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/Point32.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/PointStamped.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/Polygon.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/PolygonStamped.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/Pose.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/Pose2D.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/PoseArray.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/PoseStamped.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/PoseWithCovariance.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/PoseWithCovarianceStamped.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/Quaternion.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/QuaternionStamped.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/Transform.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/TransformStamped.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/Twist.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/TwistStamped.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/TwistWithCovariance.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/TwistWithCovarianceStamped.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/Vector3.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/Vector3Stamped.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/VelocityStamped.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/Wrench.idl", "geometry_msgs:/opt/ros/humble/share/geometry_msgs/msg/WrenchStamped.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Bool.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Byte.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/ByteMultiArray.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Char.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/ColorRGBA.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Empty.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Float32.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Float32MultiArray.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Float64.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Float64MultiArray.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Header.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Int16.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Int16MultiArray.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Int32.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Int32MultiArray.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Int64.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Int64MultiArray.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Int8.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Int8MultiArray.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/MultiArrayDimension.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/MultiArrayLayout.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/String.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/UInt16.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/UInt16MultiArray.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/UInt32.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/UInt32MultiArray.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/UInt64.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/UInt64MultiArray.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/UInt8.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/UInt8MultiArray.idl"], "target_dependencies": ["/opt/ros/humble/lib/rosidl_typesupport_fastrtps_c/rosidl_typesupport_fastrtps_c", "/opt/ros/humble/local/lib/python3.10/dist-packages/rosidl_typesupport_fastrtps_c/__init__.py", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/resource/idl__rosidl_typesupport_fastrtps_c.h.em", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/resource/idl__type_support_c.cpp.em", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/resource/msg__rosidl_typesupport_fastrtps_c.h.em", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/resource/msg__type_support_c.cpp.em", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/resource/srv__rosidl_typesupport_fastrtps_c.h.em", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/resource/srv__type_support_c.cpp.em", "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_adapter/data_interface/msg/PoseEst3d2dPointMsg.idl", "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_adapter/data_interface/msg/PoseEst3d2dPointsMsg.idl", "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_adapter/data_interface/srv/PoseEst3d2dSrv.idl", "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_adapter/data_interface/srv/CameraImage.idl", "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_adapter/data_interface/srv/InitPandaRobot.idl", "/opt/ros/humble/share/sensor_msgs/msg/BatteryState.idl", "/opt/ros/humble/share/sensor_msgs/msg/CameraInfo.idl", "/opt/ros/humble/share/sensor_msgs/msg/ChannelFloat32.idl", "/opt/ros/humble/share/sensor_msgs/msg/CompressedImage.idl", "/opt/ros/humble/share/sensor_msgs/msg/FluidPressure.idl", "/opt/ros/humble/share/sensor_msgs/msg/Illuminance.idl", "/opt/ros/humble/share/sensor_msgs/msg/Image.idl", "/opt/ros/humble/share/sensor_msgs/msg/Imu.idl", "/opt/ros/humble/share/sensor_msgs/msg/JointState.idl", "/opt/ros/humble/share/sensor_msgs/msg/Joy.idl", "/opt/ros/humble/share/sensor_msgs/msg/JoyFeedback.idl", "/opt/ros/humble/share/sensor_msgs/msg/JoyFeedbackArray.idl", "/opt/ros/humble/share/sensor_msgs/msg/LaserEcho.idl", "/opt/ros/humble/share/sensor_msgs/msg/LaserScan.idl", "/opt/ros/humble/share/sensor_msgs/msg/MagneticField.idl", "/opt/ros/humble/share/sensor_msgs/msg/MultiDOFJointState.idl", "/opt/ros/humble/share/sensor_msgs/msg/MultiEchoLaserScan.idl", "/opt/ros/humble/share/sensor_msgs/msg/NavSatFix.idl", "/opt/ros/humble/share/sensor_msgs/msg/NavSatStatus.idl", "/opt/ros/humble/share/sensor_msgs/msg/PointCloud.idl", "/opt/ros/humble/share/sensor_msgs/msg/PointCloud2.idl", "/opt/ros/humble/share/sensor_msgs/msg/PointField.idl", "/opt/ros/humble/share/sensor_msgs/msg/Range.idl", "/opt/ros/humble/share/sensor_msgs/msg/RegionOfInterest.idl", "/opt/ros/humble/share/sensor_msgs/msg/RelativeHumidity.idl", "/opt/ros/humble/share/sensor_msgs/msg/Temperature.idl", "/opt/ros/humble/share/sensor_msgs/msg/TimeReference.idl", "/opt/ros/humble/share/sensor_msgs/srv/SetCameraInfo.idl", "/opt/ros/humble/share/builtin_interfaces/msg/Duration.idl", "/opt/ros/humble/share/builtin_interfaces/msg/Time.idl", "/opt/ros/humble/share/geometry_msgs/msg/Accel.idl", "/opt/ros/humble/share/geometry_msgs/msg/AccelStamped.idl", "/opt/ros/humble/share/geometry_msgs/msg/AccelWithCovariance.idl", "/opt/ros/humble/share/geometry_msgs/msg/AccelWithCovarianceStamped.idl", "/opt/ros/humble/share/geometry_msgs/msg/Inertia.idl", "/opt/ros/humble/share/geometry_msgs/msg/InertiaStamped.idl", "/opt/ros/humble/share/geometry_msgs/msg/Point.idl", "/opt/ros/humble/share/geometry_msgs/msg/Point32.idl", "/opt/ros/humble/share/geometry_msgs/msg/PointStamped.idl", "/opt/ros/humble/share/geometry_msgs/msg/Polygon.idl", "/opt/ros/humble/share/geometry_msgs/msg/PolygonStamped.idl", "/opt/ros/humble/share/geometry_msgs/msg/Pose.idl", "/opt/ros/humble/share/geometry_msgs/msg/Pose2D.idl", "/opt/ros/humble/share/geometry_msgs/msg/PoseArray.idl", "/opt/ros/humble/share/geometry_msgs/msg/PoseStamped.idl", "/opt/ros/humble/share/geometry_msgs/msg/PoseWithCovariance.idl", "/opt/ros/humble/share/geometry_msgs/msg/PoseWithCovarianceStamped.idl", "/opt/ros/humble/share/geometry_msgs/msg/Quaternion.idl", "/opt/ros/humble/share/geometry_msgs/msg/QuaternionStamped.idl", "/opt/ros/humble/share/geometry_msgs/msg/Transform.idl", "/opt/ros/humble/share/geometry_msgs/msg/TransformStamped.idl", "/opt/ros/humble/share/geometry_msgs/msg/Twist.idl", "/opt/ros/humble/share/geometry_msgs/msg/TwistStamped.idl", "/opt/ros/humble/share/geometry_msgs/msg/TwistWithCovariance.idl", "/opt/ros/humble/share/geometry_msgs/msg/TwistWithCovarianceStamped.idl", "/opt/ros/humble/share/geometry_msgs/msg/Vector3.idl", "/opt/ros/humble/share/geometry_msgs/msg/Vector3Stamped.idl", "/opt/ros/humble/share/geometry_msgs/msg/VelocityStamped.idl", "/opt/ros/humble/share/geometry_msgs/msg/Wrench.idl", "/opt/ros/humble/share/geometry_msgs/msg/WrenchStamped.idl", "/opt/ros/humble/share/std_msgs/msg/Bool.idl", "/opt/ros/humble/share/std_msgs/msg/Byte.idl", "/opt/ros/humble/share/std_msgs/msg/ByteMultiArray.idl", "/opt/ros/humble/share/std_msgs/msg/Char.idl", "/opt/ros/humble/share/std_msgs/msg/ColorRGBA.idl", "/opt/ros/humble/share/std_msgs/msg/Empty.idl", "/opt/ros/humble/share/std_msgs/msg/Float32.idl", "/opt/ros/humble/share/std_msgs/msg/Float32MultiArray.idl", "/opt/ros/humble/share/std_msgs/msg/Float64.idl", "/opt/ros/humble/share/std_msgs/msg/Float64MultiArray.idl", "/opt/ros/humble/share/std_msgs/msg/Header.idl", "/opt/ros/humble/share/std_msgs/msg/Int16.idl", "/opt/ros/humble/share/std_msgs/msg/Int16MultiArray.idl", "/opt/ros/humble/share/std_msgs/msg/Int32.idl", "/opt/ros/humble/share/std_msgs/msg/Int32MultiArray.idl", "/opt/ros/humble/share/std_msgs/msg/Int64.idl", "/opt/ros/humble/share/std_msgs/msg/Int64MultiArray.idl", "/opt/ros/humble/share/std_msgs/msg/Int8.idl", "/opt/ros/humble/share/std_msgs/msg/Int8MultiArray.idl", "/opt/ros/humble/share/std_msgs/msg/MultiArrayDimension.idl", "/opt/ros/humble/share/std_msgs/msg/MultiArrayLayout.idl", "/opt/ros/humble/share/std_msgs/msg/String.idl", "/opt/ros/humble/share/std_msgs/msg/UInt16.idl", "/opt/ros/humble/share/std_msgs/msg/UInt16MultiArray.idl", "/opt/ros/humble/share/std_msgs/msg/UInt32.idl", "/opt/ros/humble/share/std_msgs/msg/UInt32MultiArray.idl", "/opt/ros/humble/share/std_msgs/msg/UInt64.idl", "/opt/ros/humble/share/std_msgs/msg/UInt64MultiArray.idl", "/opt/ros/humble/share/std_msgs/msg/UInt8.idl", "/opt/ros/humble/share/std_msgs/msg/UInt8MultiArray.idl"]}