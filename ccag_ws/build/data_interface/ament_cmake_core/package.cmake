set(_AMENT_PACKAGE_NAME "data_interface")
set(data_interface_VERSION "0.0.0")
set(data_interface_MAINTAINER "simon <<EMAIL>>")
set(data_interface_BUILD_DEPENDS "rosidl_default_generators" "sensor_msgs" "std_msgs")
set(data_interface_BUILDTOOL_DEPENDS "ament_cmake")
set(data_interface_BUILD_EXPORT_DEPENDS "sensor_msgs" "std_msgs")
set(data_interface_BUILDTOOL_EXPORT_DEPENDS )
set(data_interface_EXEC_DEPENDS "rosidl_default_runtime" "sensor_msgs" "std_msgs")
set(data_interface_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(data_interface_GROUP_DEPENDS )
set(data_interface_MEMBER_OF_GROUPS "rosidl_interface_packages")
set(data_interface_DEPRECATED "")
set(data_interface_EXPORT_TAGS)
list(APPEND data_interface_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
