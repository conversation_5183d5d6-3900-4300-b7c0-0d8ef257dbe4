// generated from rosidl_typesupport_introspection_c/resource/idl__type_support.c.em
// with input from data_interface:msg/PoseEst3d2dPointsMsg.idl
// generated code does not contain a copyright notice

#include <stddef.h>
#include "data_interface/msg/detail/pose_est3d2d_points_msg__rosidl_typesupport_introspection_c.h"
#include "data_interface/msg/rosidl_typesupport_introspection_c__visibility_control.h"
#include "rosidl_typesupport_introspection_c/field_types.h"
#include "rosidl_typesupport_introspection_c/identifier.h"
#include "rosidl_typesupport_introspection_c/message_introspection.h"
#include "data_interface/msg/detail/pose_est3d2d_points_msg__functions.h"
#include "data_interface/msg/detail/pose_est3d2d_points_msg__struct.h"


// Include directives for member types
// Member `points`
#include "data_interface/msg/pose_est3d2d_point_msg.h"
// Member `points`
#include "data_interface/msg/detail/pose_est3d2d_point_msg__rosidl_typesupport_introspection_c.h"

#ifdef __cplusplus
extern "C"
{
#endif

void data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointsMsg_init_function(
  void * message_memory, enum rosidl_runtime_c__message_initialization _init)
{
  // TODO(karsten1987): initializers are not yet implemented for typesupport c
  // see https://github.com/ros2/ros2/issues/397
  (void) _init;
  data_interface__msg__PoseEst3d2dPointsMsg__init(message_memory);
}

void data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointsMsg_fini_function(void * message_memory)
{
  data_interface__msg__PoseEst3d2dPointsMsg__fini(message_memory);
}

size_t data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__size_function__PoseEst3d2dPointsMsg__points(
  const void * untyped_member)
{
  (void)untyped_member;
  return 5;
}

const void * data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__get_const_function__PoseEst3d2dPointsMsg__points(
  const void * untyped_member, size_t index)
{
  const data_interface__msg__PoseEst3d2dPointMsg * member =
    (const data_interface__msg__PoseEst3d2dPointMsg *)(untyped_member);
  return &member[index];
}

void * data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__get_function__PoseEst3d2dPointsMsg__points(
  void * untyped_member, size_t index)
{
  data_interface__msg__PoseEst3d2dPointMsg * member =
    (data_interface__msg__PoseEst3d2dPointMsg *)(untyped_member);
  return &member[index];
}

void data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__fetch_function__PoseEst3d2dPointsMsg__points(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const data_interface__msg__PoseEst3d2dPointMsg * item =
    ((const data_interface__msg__PoseEst3d2dPointMsg *)
    data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__get_const_function__PoseEst3d2dPointsMsg__points(untyped_member, index));
  data_interface__msg__PoseEst3d2dPointMsg * value =
    (data_interface__msg__PoseEst3d2dPointMsg *)(untyped_value);
  *value = *item;
}

void data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__assign_function__PoseEst3d2dPointsMsg__points(
  void * untyped_member, size_t index, const void * untyped_value)
{
  data_interface__msg__PoseEst3d2dPointMsg * item =
    ((data_interface__msg__PoseEst3d2dPointMsg *)
    data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__get_function__PoseEst3d2dPointsMsg__points(untyped_member, index));
  const data_interface__msg__PoseEst3d2dPointMsg * value =
    (const data_interface__msg__PoseEst3d2dPointMsg *)(untyped_value);
  *item = *value;
}

static rosidl_typesupport_introspection_c__MessageMember data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointsMsg_message_member_array[1] = {
  {
    "points",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    true,  // is array
    5,  // array size
    false,  // is upper bound
    offsetof(data_interface__msg__PoseEst3d2dPointsMsg, points),  // bytes offset in struct
    NULL,  // default value
    data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__size_function__PoseEst3d2dPointsMsg__points,  // size() function pointer
    data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__get_const_function__PoseEst3d2dPointsMsg__points,  // get_const(index) function pointer
    data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__get_function__PoseEst3d2dPointsMsg__points,  // get(index) function pointer
    data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__fetch_function__PoseEst3d2dPointsMsg__points,  // fetch(index, &value) function pointer
    data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__assign_function__PoseEst3d2dPointsMsg__points,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  }
};

static const rosidl_typesupport_introspection_c__MessageMembers data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointsMsg_message_members = {
  "data_interface__msg",  // message namespace
  "PoseEst3d2dPointsMsg",  // message name
  1,  // number of fields
  sizeof(data_interface__msg__PoseEst3d2dPointsMsg),
  data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointsMsg_message_member_array,  // message members
  data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointsMsg_init_function,  // function to initialize message memory (memory has to be allocated)
  data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointsMsg_fini_function  // function to terminate message instance (will not free memory)
};

// this is not const since it must be initialized on first access
// since C does not allow non-integral compile-time constants
static rosidl_message_type_support_t data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointsMsg_message_type_support_handle = {
  0,
  &data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointsMsg_message_members,
  get_message_typesupport_handle_function,
};

ROSIDL_TYPESUPPORT_INTROSPECTION_C_EXPORT_data_interface
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, data_interface, msg, PoseEst3d2dPointsMsg)() {
  data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointsMsg_message_member_array[0].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, data_interface, msg, PoseEst3d2dPointMsg)();
  if (!data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointsMsg_message_type_support_handle.typesupport_identifier) {
    data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointsMsg_message_type_support_handle.typesupport_identifier =
      rosidl_typesupport_introspection_c__identifier;
  }
  return &data_interface__msg__PoseEst3d2dPointsMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointsMsg_message_type_support_handle;
}
#ifdef __cplusplus
}
#endif
