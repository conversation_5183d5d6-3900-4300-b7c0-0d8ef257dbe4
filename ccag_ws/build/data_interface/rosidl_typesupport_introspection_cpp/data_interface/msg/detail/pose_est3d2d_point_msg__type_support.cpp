// generated from rosidl_typesupport_introspection_cpp/resource/idl__type_support.cpp.em
// with input from data_interface:msg/PoseEst3d2dPointMsg.idl
// generated code does not contain a copyright notice

#include "array"
#include "cstddef"
#include "string"
#include "vector"
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_cpp/message_type_support.hpp"
#include "rosidl_typesupport_interface/macros.h"
#include "data_interface/msg/detail/pose_est3d2d_point_msg__struct.hpp"
#include "rosidl_typesupport_introspection_cpp/field_types.hpp"
#include "rosidl_typesupport_introspection_cpp/identifier.hpp"
#include "rosidl_typesupport_introspection_cpp/message_introspection.hpp"
#include "rosidl_typesupport_introspection_cpp/message_type_support_decl.hpp"
#include "rosidl_typesupport_introspection_cpp/visibility_control.h"

namespace data_interface
{

namespace msg
{

namespace rosidl_typesupport_introspection_cpp
{

void PoseEst3d2dPointMsg_init_function(
  void * message_memory, rosidl_runtime_cpp::MessageInitialization _init)
{
  new (message_memory) data_interface::msg::PoseEst3d2dPointMsg(_init);
}

void PoseEst3d2dPointMsg_fini_function(void * message_memory)
{
  auto typed_message = static_cast<data_interface::msg::PoseEst3d2dPointMsg *>(message_memory);
  typed_message->~PoseEst3d2dPointMsg();
}

static const ::rosidl_typesupport_introspection_cpp::MessageMember PoseEst3d2dPointMsg_message_member_array[3] = {
  {
    "x",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_DOUBLE,  // type
    0,  // upper bound of string
    nullptr,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(data_interface::msg::PoseEst3d2dPointMsg, x),  // bytes offset in struct
    nullptr,  // default value
    nullptr,  // size() function pointer
    nullptr,  // get_const(index) function pointer
    nullptr,  // get(index) function pointer
    nullptr,  // fetch(index, &value) function pointer
    nullptr,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  },
  {
    "y",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_DOUBLE,  // type
    0,  // upper bound of string
    nullptr,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(data_interface::msg::PoseEst3d2dPointMsg, y),  // bytes offset in struct
    nullptr,  // default value
    nullptr,  // size() function pointer
    nullptr,  // get_const(index) function pointer
    nullptr,  // get(index) function pointer
    nullptr,  // fetch(index, &value) function pointer
    nullptr,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  },
  {
    "label",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_INT8,  // type
    0,  // upper bound of string
    nullptr,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(data_interface::msg::PoseEst3d2dPointMsg, label),  // bytes offset in struct
    nullptr,  // default value
    nullptr,  // size() function pointer
    nullptr,  // get_const(index) function pointer
    nullptr,  // get(index) function pointer
    nullptr,  // fetch(index, &value) function pointer
    nullptr,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  }
};

static const ::rosidl_typesupport_introspection_cpp::MessageMembers PoseEst3d2dPointMsg_message_members = {
  "data_interface::msg",  // message namespace
  "PoseEst3d2dPointMsg",  // message name
  3,  // number of fields
  sizeof(data_interface::msg::PoseEst3d2dPointMsg),
  PoseEst3d2dPointMsg_message_member_array,  // message members
  PoseEst3d2dPointMsg_init_function,  // function to initialize message memory (memory has to be allocated)
  PoseEst3d2dPointMsg_fini_function  // function to terminate message instance (will not free memory)
};

static const rosidl_message_type_support_t PoseEst3d2dPointMsg_message_type_support_handle = {
  ::rosidl_typesupport_introspection_cpp::typesupport_identifier,
  &PoseEst3d2dPointMsg_message_members,
  get_message_typesupport_handle_function,
};

}  // namespace rosidl_typesupport_introspection_cpp

}  // namespace msg

}  // namespace data_interface


namespace rosidl_typesupport_introspection_cpp
{

template<>
ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
get_message_type_support_handle<data_interface::msg::PoseEst3d2dPointMsg>()
{
  return &::data_interface::msg::rosidl_typesupport_introspection_cpp::PoseEst3d2dPointMsg_message_type_support_handle;
}

}  // namespace rosidl_typesupport_introspection_cpp

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_cpp, data_interface, msg, PoseEst3d2dPointMsg)() {
  return &::data_interface::msg::rosidl_typesupport_introspection_cpp::PoseEst3d2dPointMsg_message_type_support_handle;
}

#ifdef __cplusplus
}
#endif
