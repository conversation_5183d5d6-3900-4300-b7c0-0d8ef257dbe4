// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from data_interface:srv/PoseEst3d2dSrv.idl
// generated code does not contain a copyright notice

#ifndef DATA_INTERFACE__SRV__DETAIL__POSE_EST3D2D_SRV__STRUCT_H_
#define DATA_INTERFACE__SRV__DETAIL__POSE_EST3D2D_SRV__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'points'
#include "data_interface/msg/detail/pose_est3d2d_points_msg__struct.h"

/// Struct defined in srv/PoseEst3d2dSrv in the package data_interface.
typedef struct data_interface__srv__PoseEst3d2dSrv_Request
{
  data_interface__msg__PoseEst3d2dPointsMsg points;
} data_interface__srv__PoseEst3d2dSrv_Request;

// Struct for a sequence of data_interface__srv__PoseEst3d2dSrv_Request.
typedef struct data_interface__srv__PoseEst3d2dSrv_Request__Sequence
{
  data_interface__srv__PoseEst3d2dSrv_Request * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} data_interface__srv__PoseEst3d2dSrv_Request__Sequence;


// Constants defined in the message

// Include directives for member types
// Member 'pose'
#include "geometry_msgs/msg/detail/pose_stamped__struct.h"

/// Struct defined in srv/PoseEst3d2dSrv in the package data_interface.
typedef struct data_interface__srv__PoseEst3d2dSrv_Response
{
  geometry_msgs__msg__PoseStamped pose;
} data_interface__srv__PoseEst3d2dSrv_Response;

// Struct for a sequence of data_interface__srv__PoseEst3d2dSrv_Response.
typedef struct data_interface__srv__PoseEst3d2dSrv_Response__Sequence
{
  data_interface__srv__PoseEst3d2dSrv_Response * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} data_interface__srv__PoseEst3d2dSrv_Response__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // DATA_INTERFACE__SRV__DETAIL__POSE_EST3D2D_SRV__STRUCT_H_
