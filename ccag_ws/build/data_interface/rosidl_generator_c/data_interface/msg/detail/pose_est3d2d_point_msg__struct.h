// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from data_interface:msg/PoseEst3d2dPointMsg.idl
// generated code does not contain a copyright notice

#ifndef DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINT_MSG__STRUCT_H_
#define DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINT_MSG__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/PoseEst3d2dPointMsg in the package data_interface.
/**
  * PoseEst3d2dPointsMsg.msg
 */
typedef struct data_interface__msg__PoseEst3d2dPointMsg
{
  double x;
  double y;
  int8_t label;
} data_interface__msg__PoseEst3d2dPointMsg;

// Struct for a sequence of data_interface__msg__PoseEst3d2dPointMsg.
typedef struct data_interface__msg__PoseEst3d2dPointMsg__Sequence
{
  data_interface__msg__PoseEst3d2dPointMsg * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} data_interface__msg__PoseEst3d2dPointMsg__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINT_MSG__STRUCT_H_
