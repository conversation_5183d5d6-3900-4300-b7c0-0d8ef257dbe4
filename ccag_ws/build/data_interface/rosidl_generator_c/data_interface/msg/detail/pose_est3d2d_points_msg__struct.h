// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from data_interface:msg/PoseEst3d2dPointsMsg.idl
// generated code does not contain a copyright notice

#ifndef DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINTS_MSG__STRUCT_H_
#define DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINTS_MSG__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'points'
#include "data_interface/msg/detail/pose_est3d2d_point_msg__struct.h"

/// Struct defined in msg/PoseEst3d2dPointsMsg in the package data_interface.
/**
  * PoseEst3d2dPointsMsg.msg
 */
typedef struct data_interface__msg__PoseEst3d2dPointsMsg
{
  data_interface__msg__PoseEst3d2dPointMsg points[5];
} data_interface__msg__PoseEst3d2dPointsMsg;

// Struct for a sequence of data_interface__msg__PoseEst3d2dPointsMsg.
typedef struct data_interface__msg__PoseEst3d2dPointsMsg__Sequence
{
  data_interface__msg__PoseEst3d2dPointsMsg * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} data_interface__msg__PoseEst3d2dPointsMsg__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINTS_MSG__STRUCT_H_
