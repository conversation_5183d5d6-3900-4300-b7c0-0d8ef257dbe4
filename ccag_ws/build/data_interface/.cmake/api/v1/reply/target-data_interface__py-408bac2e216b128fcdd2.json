{"backtrace": 1, "backtraceGraph": {"commands": ["add_custom_target"], "files": ["/home/<USER>/ccag/ccag_ws/build/data_interface/data_interface__py/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 34, "parent": 0}]}, "dependencies": [{"id": "data_interface::@6890427a1f51a3e7e1df"}], "id": "data_interface__py::@255962fb3cb9f93bf632", "name": "data_interface__py", "paths": {"build": "data_interface__py", "source": "/home/<USER>/ccag/ccag_ws/build/data_interface/data_interface__py"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 1, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/data_interface__py/CMakeFiles/data_interface__py", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/data_interface__py/CMakeFiles/data_interface__py.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_py/data_interface/_data_interface_s.ep.rosidl_typesupport_fastrtps_c.c.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}