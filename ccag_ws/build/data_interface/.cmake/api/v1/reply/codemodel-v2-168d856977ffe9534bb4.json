{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-53fd8e397d15fa3195f4.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}, {"build": "data_interface__py", "jsonFile": "directory-data_interface__py-04ed875aee36c21a27c1.json", "minimumCMakeVersion": {"string": "3.12"}, "parentIndex": 0, "projectIndex": 0, "source": "/home/<USER>/ccag/ccag_ws/build/data_interface/data_interface__py", "targetIndexes": [4]}], "name": "", "projects": [{"directoryIndexes": [0, 1], "name": "data_interface", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}], "targets": [{"directoryIndex": 0, "id": "ament_cmake_python_build_data_interface_egg::@6890427a1f51a3e7e1df", "jsonFile": "target-ament_cmake_python_build_data_interface_egg-6d0a2295dfe7e91a69b5.json", "name": "ament_cmake_python_build_data_interface_egg", "projectIndex": 0}, {"directoryIndex": 0, "id": "ament_cmake_python_copy_data_interface::@6890427a1f51a3e7e1df", "jsonFile": "target-ament_cmake_python_copy_data_interface-f12482cd30f4a5d689c7.json", "name": "ament_cmake_python_copy_data_interface", "projectIndex": 0}, {"directoryIndex": 0, "id": "data_interface::@6890427a1f51a3e7e1df", "jsonFile": "target-data_interface-9492a579e85ee56327cc.json", "name": "data_interface", "projectIndex": 0}, {"directoryIndex": 0, "id": "data_interface__cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-data_interface__cpp-5f99aaee87a1e0ea9569.json", "name": "data_interface__cpp", "projectIndex": 0}, {"directoryIndex": 1, "id": "data_interface__py::@255962fb3cb9f93bf632", "jsonFile": "target-data_interface__py-408bac2e216b128fcdd2.json", "name": "data_interface__py", "projectIndex": 0}, {"directoryIndex": 0, "id": "data_interface__rosidl_generator_c::@6890427a1f51a3e7e1df", "jsonFile": "target-data_interface__rosidl_generator_c-6503ff8f31c64f1c9028.json", "name": "data_interface__rosidl_generator_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "data_interface__rosidl_generator_py::@6890427a1f51a3e7e1df", "jsonFile": "target-data_interface__rosidl_generator_py-386531c432345df0a0c2.json", "name": "data_interface__rosidl_generator_py", "projectIndex": 0}, {"directoryIndex": 0, "id": "data_interface__rosidl_typesupport_c::@6890427a1f51a3e7e1df", "jsonFile": "target-data_interface__rosidl_typesupport_c-9cc495b9699394e858b1.json", "name": "data_interface__rosidl_typesupport_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "data_interface__rosidl_typesupport_c__pyext::@6890427a1f51a3e7e1df", "jsonFile": "target-data_interface__rosidl_typesupport_c__pyext-9e5b64306816ec94fbbe.json", "name": "data_interface__rosidl_typesupport_c__pyext", "projectIndex": 0}, {"directoryIndex": 0, "id": "data_interface__rosidl_typesupport_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-data_interface__rosidl_typesupport_cpp-e520b7be49e4142ca891.json", "name": "data_interface__rosidl_typesupport_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "data_interface__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df", "jsonFile": "target-data_interface__rosidl_typesupport_fastrtps_c-5e9d1681348a73f824da.json", "name": "data_interface__rosidl_typesupport_fastrtps_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "data_interface__rosidl_typesupport_fastrtps_c__pyext::@6890427a1f51a3e7e1df", "jsonFile": "target-data_interface__rosidl_typesupport_fastrtps_c__pyext-a8a3120eb9ab6690dc18.json", "name": "data_interface__rosidl_typesupport_fastrtps_c__pyext", "projectIndex": 0}, {"directoryIndex": 0, "id": "data_interface__rosidl_typesupport_fastrtps_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-data_interface__rosidl_typesupport_fastrtps_cpp-361ad63cdfe7d041692d.json", "name": "data_interface__rosidl_typesupport_fastrtps_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "data_interface__rosidl_typesupport_introspection_c::@6890427a1f51a3e7e1df", "jsonFile": "target-data_interface__rosidl_typesupport_introspection_c-9f221d0fea58a7fcd9e2.json", "name": "data_interface__rosidl_typesupport_introspection_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "data_interface__rosidl_typesupport_introspection_c__pyext::@6890427a1f51a3e7e1df", "jsonFile": "target-data_interface__rosidl_typesupport_introspection_c__pyext-d20a0c7a86974c2f50a9.json", "name": "data_interface__rosidl_typesupport_introspection_c__pyext", "projectIndex": 0}, {"directoryIndex": 0, "id": "data_interface__rosidl_typesupport_introspection_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-data_interface__rosidl_typesupport_introspection_cpp-2695c751bd692c3b0c9f.json", "name": "data_interface__rosidl_typesupport_introspection_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "data_interface_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-data_interface_uninstall-6a967c1d9c31b4ec88fb.json", "name": "data_interface_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-28fceb609d984a348e58.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/ccag/ccag_ws/build/data_interface", "source": "/home/<USER>/ccag/ccag_ws/src/data_interface"}, "version": {"major": 2, "minor": 3}}