{"backtrace": 5, "backtraceGraph": {"commands": ["add_custom_target", "include", "ament_execute_extensions", "rosidl_generate_interfaces"], "files": ["/opt/ros/humble/share/rosidl_generator_cpp/cmake/rosidl_generator_cpp_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 16, "parent": 0}, {"command": 2, "file": 2, "line": 286, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 109, "parent": 4}]}, "id": "data_interface__cpp::@6890427a1f51a3e7e1df", "name": "data_interface__cpp", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/CMakeFiles/data_interface__cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/CMakeFiles/data_interface__cpp.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_cpp/data_interface/msg/pose_est3d2d_point_msg.hpp.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}