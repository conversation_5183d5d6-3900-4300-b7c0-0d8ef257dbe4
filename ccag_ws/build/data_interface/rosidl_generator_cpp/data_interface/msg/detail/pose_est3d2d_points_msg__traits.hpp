// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from data_interface:msg/PoseEst3d2dPointsMsg.idl
// generated code does not contain a copyright notice

#ifndef DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINTS_MSG__TRAITS_HPP_
#define DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINTS_MSG__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "data_interface/msg/detail/pose_est3d2d_points_msg__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'points'
#include "data_interface/msg/detail/pose_est3d2d_point_msg__traits.hpp"

namespace data_interface
{

namespace msg
{

inline void to_flow_style_yaml(
  const PoseEst3d2dPointsMsg & msg,
  std::ostream & out)
{
  out << "{";
  // member: points
  {
    if (msg.points.size() == 0) {
      out << "points: []";
    } else {
      out << "points: [";
      size_t pending_items = msg.points.size();
      for (auto item : msg.points) {
        to_flow_style_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const PoseEst3d2dPointsMsg & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: points
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.points.size() == 0) {
      out << "points: []\n";
    } else {
      out << "points:\n";
      for (auto item : msg.points) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "-\n";
        to_block_style_yaml(item, out, indentation + 2);
      }
    }
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const PoseEst3d2dPointsMsg & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace data_interface

namespace rosidl_generator_traits
{

[[deprecated("use data_interface::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const data_interface::msg::PoseEst3d2dPointsMsg & msg,
  std::ostream & out, size_t indentation = 0)
{
  data_interface::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use data_interface::msg::to_yaml() instead")]]
inline std::string to_yaml(const data_interface::msg::PoseEst3d2dPointsMsg & msg)
{
  return data_interface::msg::to_yaml(msg);
}

template<>
inline const char * data_type<data_interface::msg::PoseEst3d2dPointsMsg>()
{
  return "data_interface::msg::PoseEst3d2dPointsMsg";
}

template<>
inline const char * name<data_interface::msg::PoseEst3d2dPointsMsg>()
{
  return "data_interface/msg/PoseEst3d2dPointsMsg";
}

template<>
struct has_fixed_size<data_interface::msg::PoseEst3d2dPointsMsg>
  : std::integral_constant<bool, has_fixed_size<data_interface::msg::PoseEst3d2dPointMsg>::value> {};

template<>
struct has_bounded_size<data_interface::msg::PoseEst3d2dPointsMsg>
  : std::integral_constant<bool, has_bounded_size<data_interface::msg::PoseEst3d2dPointMsg>::value> {};

template<>
struct is_message<data_interface::msg::PoseEst3d2dPointsMsg>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINTS_MSG__TRAITS_HPP_
