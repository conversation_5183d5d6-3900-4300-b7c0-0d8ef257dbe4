// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from data_interface:msg/PoseEst3d2dPointsMsg.idl
// generated code does not contain a copyright notice

#ifndef DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINTS_MSG__STRUCT_HPP_
#define DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINTS_MSG__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'points'
#include "data_interface/msg/detail/pose_est3d2d_point_msg__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__data_interface__msg__PoseEst3d2dPointsMsg __attribute__((deprecated))
#else
# define DEPRECATED__data_interface__msg__PoseEst3d2dPointsMsg __declspec(deprecated)
#endif

namespace data_interface
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct PoseEst3d2dPointsMsg_
{
  using Type = PoseEst3d2dPointsMsg_<ContainerAllocator>;

  explicit PoseEst3d2dPointsMsg_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->points.fill(data_interface::msg::PoseEst3d2dPointMsg_<ContainerAllocator>{_init});
    }
  }

  explicit PoseEst3d2dPointsMsg_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : points(_alloc)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->points.fill(data_interface::msg::PoseEst3d2dPointMsg_<ContainerAllocator>{_alloc, _init});
    }
  }

  // field types and members
  using _points_type =
    std::array<data_interface::msg::PoseEst3d2dPointMsg_<ContainerAllocator>, 5>;
  _points_type points;

  // setters for named parameter idiom
  Type & set__points(
    const std::array<data_interface::msg::PoseEst3d2dPointMsg_<ContainerAllocator>, 5> & _arg)
  {
    this->points = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    data_interface::msg::PoseEst3d2dPointsMsg_<ContainerAllocator> *;
  using ConstRawPtr =
    const data_interface::msg::PoseEst3d2dPointsMsg_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<data_interface::msg::PoseEst3d2dPointsMsg_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<data_interface::msg::PoseEst3d2dPointsMsg_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      data_interface::msg::PoseEst3d2dPointsMsg_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<data_interface::msg::PoseEst3d2dPointsMsg_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      data_interface::msg::PoseEst3d2dPointsMsg_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<data_interface::msg::PoseEst3d2dPointsMsg_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<data_interface::msg::PoseEst3d2dPointsMsg_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<data_interface::msg::PoseEst3d2dPointsMsg_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__data_interface__msg__PoseEst3d2dPointsMsg
    std::shared_ptr<data_interface::msg::PoseEst3d2dPointsMsg_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__data_interface__msg__PoseEst3d2dPointsMsg
    std::shared_ptr<data_interface::msg::PoseEst3d2dPointsMsg_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const PoseEst3d2dPointsMsg_ & other) const
  {
    if (this->points != other.points) {
      return false;
    }
    return true;
  }
  bool operator!=(const PoseEst3d2dPointsMsg_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct PoseEst3d2dPointsMsg_

// alias to use template instance with default allocator
using PoseEst3d2dPointsMsg =
  data_interface::msg::PoseEst3d2dPointsMsg_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace data_interface

#endif  // DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINTS_MSG__STRUCT_HPP_
