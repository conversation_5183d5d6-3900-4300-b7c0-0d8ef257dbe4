// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from data_interface:srv/CameraImage.idl
// generated code does not contain a copyright notice

#ifndef DATA_INTERFACE__SRV__DETAIL__CAMERA_IMAGE__STRUCT_HPP_
#define DATA_INTERFACE__SRV__DETAIL__CAMERA_IMAGE__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__data_interface__srv__CameraImage_Request __attribute__((deprecated))
#else
# define DEPRECATED__data_interface__srv__CameraImage_Request __declspec(deprecated)
#endif

namespace data_interface
{

namespace srv
{

// message struct
template<class ContainerAllocator>
struct CameraImage_Request_
{
  using Type = CameraImage_Request_<ContainerAllocator>;

  explicit CameraImage_Request_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->get = false;
    }
  }

  explicit CameraImage_Request_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->get = false;
    }
  }

  // field types and members
  using _get_type =
    bool;
  _get_type get;

  // setters for named parameter idiom
  Type & set__get(
    const bool & _arg)
  {
    this->get = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    data_interface::srv::CameraImage_Request_<ContainerAllocator> *;
  using ConstRawPtr =
    const data_interface::srv::CameraImage_Request_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<data_interface::srv::CameraImage_Request_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<data_interface::srv::CameraImage_Request_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      data_interface::srv::CameraImage_Request_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<data_interface::srv::CameraImage_Request_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      data_interface::srv::CameraImage_Request_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<data_interface::srv::CameraImage_Request_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<data_interface::srv::CameraImage_Request_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<data_interface::srv::CameraImage_Request_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__data_interface__srv__CameraImage_Request
    std::shared_ptr<data_interface::srv::CameraImage_Request_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__data_interface__srv__CameraImage_Request
    std::shared_ptr<data_interface::srv::CameraImage_Request_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const CameraImage_Request_ & other) const
  {
    if (this->get != other.get) {
      return false;
    }
    return true;
  }
  bool operator!=(const CameraImage_Request_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct CameraImage_Request_

// alias to use template instance with default allocator
using CameraImage_Request =
  data_interface::srv::CameraImage_Request_<std::allocator<void>>;

// constant definitions

}  // namespace srv

}  // namespace data_interface


// Include directives for member types
// Member 'server_image'
#include "sensor_msgs/msg/detail/image__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__data_interface__srv__CameraImage_Response __attribute__((deprecated))
#else
# define DEPRECATED__data_interface__srv__CameraImage_Response __declspec(deprecated)
#endif

namespace data_interface
{

namespace srv
{

// message struct
template<class ContainerAllocator>
struct CameraImage_Response_
{
  using Type = CameraImage_Response_<ContainerAllocator>;

  explicit CameraImage_Response_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : server_image(_init)
  {
    (void)_init;
  }

  explicit CameraImage_Response_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : server_image(_alloc, _init)
  {
    (void)_init;
  }

  // field types and members
  using _server_image_type =
    sensor_msgs::msg::Image_<ContainerAllocator>;
  _server_image_type server_image;

  // setters for named parameter idiom
  Type & set__server_image(
    const sensor_msgs::msg::Image_<ContainerAllocator> & _arg)
  {
    this->server_image = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    data_interface::srv::CameraImage_Response_<ContainerAllocator> *;
  using ConstRawPtr =
    const data_interface::srv::CameraImage_Response_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<data_interface::srv::CameraImage_Response_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<data_interface::srv::CameraImage_Response_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      data_interface::srv::CameraImage_Response_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<data_interface::srv::CameraImage_Response_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      data_interface::srv::CameraImage_Response_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<data_interface::srv::CameraImage_Response_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<data_interface::srv::CameraImage_Response_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<data_interface::srv::CameraImage_Response_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__data_interface__srv__CameraImage_Response
    std::shared_ptr<data_interface::srv::CameraImage_Response_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__data_interface__srv__CameraImage_Response
    std::shared_ptr<data_interface::srv::CameraImage_Response_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const CameraImage_Response_ & other) const
  {
    if (this->server_image != other.server_image) {
      return false;
    }
    return true;
  }
  bool operator!=(const CameraImage_Response_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct CameraImage_Response_

// alias to use template instance with default allocator
using CameraImage_Response =
  data_interface::srv::CameraImage_Response_<std::allocator<void>>;

// constant definitions

}  // namespace srv

}  // namespace data_interface

namespace data_interface
{

namespace srv
{

struct CameraImage
{
  using Request = data_interface::srv::CameraImage_Request;
  using Response = data_interface::srv::CameraImage_Response;
};

}  // namespace srv

}  // namespace data_interface

#endif  // DATA_INTERFACE__SRV__DETAIL__CAMERA_IMAGE__STRUCT_HPP_
