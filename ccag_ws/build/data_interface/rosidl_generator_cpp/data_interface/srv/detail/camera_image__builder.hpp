// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from data_interface:srv/CameraImage.idl
// generated code does not contain a copyright notice

#ifndef DATA_INTERFACE__SRV__DETAIL__CAMERA_IMAGE__BUILDER_HPP_
#define DATA_INTERFACE__SRV__DETAIL__CAMERA_IMAGE__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "data_interface/srv/detail/camera_image__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace data_interface
{

namespace srv
{

namespace builder
{

class Init_CameraImage_Request_get
{
public:
  Init_CameraImage_Request_get()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  ::data_interface::srv::CameraImage_Request get(::data_interface::srv::CameraImage_Request::_get_type arg)
  {
    msg_.get = std::move(arg);
    return std::move(msg_);
  }

private:
  ::data_interface::srv::CameraImage_Request msg_;
};

}  // namespace builder

}  // namespace srv

template<typename MessageType>
auto build();

template<>
inline
auto build<::data_interface::srv::CameraImage_Request>()
{
  return data_interface::srv::builder::Init_CameraImage_Request_get();
}

}  // namespace data_interface


namespace data_interface
{

namespace srv
{

namespace builder
{

class Init_CameraImage_Response_server_image
{
public:
  Init_CameraImage_Response_server_image()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  ::data_interface::srv::CameraImage_Response server_image(::data_interface::srv::CameraImage_Response::_server_image_type arg)
  {
    msg_.server_image = std::move(arg);
    return std::move(msg_);
  }

private:
  ::data_interface::srv::CameraImage_Response msg_;
};

}  // namespace builder

}  // namespace srv

template<typename MessageType>
auto build();

template<>
inline
auto build<::data_interface::srv::CameraImage_Response>()
{
  return data_interface::srv::builder::Init_CameraImage_Response_server_image();
}

}  // namespace data_interface

#endif  // DATA_INTERFACE__SRV__DETAIL__CAMERA_IMAGE__BUILDER_HPP_
