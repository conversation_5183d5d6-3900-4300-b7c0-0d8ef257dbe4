// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from data_interface:srv/InitPandaRobot.idl
// generated code does not contain a copyright notice

#ifndef DATA_INTERFACE__SRV__DETAIL__INIT_PANDA_ROBOT__TRAITS_HPP_
#define DATA_INTERFACE__SRV__DETAIL__INIT_PANDA_ROBOT__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "data_interface/srv/detail/init_panda_robot__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace data_interface
{

namespace srv
{

inline void to_flow_style_yaml(
  const InitPandaRobot_Request & msg,
  std::ostream & out)
{
  out << "{";
  // member: request_string
  {
    out << "request_string: ";
    rosidl_generator_traits::value_to_yaml(msg.request_string, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const InitPandaRobot_Request & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: request_string
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "request_string: ";
    rosidl_generator_traits::value_to_yaml(msg.request_string, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const InitPandaRobot_Request & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace srv

}  // namespace data_interface

namespace rosidl_generator_traits
{

[[deprecated("use data_interface::srv::to_block_style_yaml() instead")]]
inline void to_yaml(
  const data_interface::srv::InitPandaRobot_Request & msg,
  std::ostream & out, size_t indentation = 0)
{
  data_interface::srv::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use data_interface::srv::to_yaml() instead")]]
inline std::string to_yaml(const data_interface::srv::InitPandaRobot_Request & msg)
{
  return data_interface::srv::to_yaml(msg);
}

template<>
inline const char * data_type<data_interface::srv::InitPandaRobot_Request>()
{
  return "data_interface::srv::InitPandaRobot_Request";
}

template<>
inline const char * name<data_interface::srv::InitPandaRobot_Request>()
{
  return "data_interface/srv/InitPandaRobot_Request";
}

template<>
struct has_fixed_size<data_interface::srv::InitPandaRobot_Request>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<data_interface::srv::InitPandaRobot_Request>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<data_interface::srv::InitPandaRobot_Request>
  : std::true_type {};

}  // namespace rosidl_generator_traits

namespace data_interface
{

namespace srv
{

inline void to_flow_style_yaml(
  const InitPandaRobot_Response & msg,
  std::ostream & out)
{
  out << "{";
  // member: response_string
  {
    out << "response_string: ";
    rosidl_generator_traits::value_to_yaml(msg.response_string, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const InitPandaRobot_Response & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: response_string
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "response_string: ";
    rosidl_generator_traits::value_to_yaml(msg.response_string, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const InitPandaRobot_Response & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace srv

}  // namespace data_interface

namespace rosidl_generator_traits
{

[[deprecated("use data_interface::srv::to_block_style_yaml() instead")]]
inline void to_yaml(
  const data_interface::srv::InitPandaRobot_Response & msg,
  std::ostream & out, size_t indentation = 0)
{
  data_interface::srv::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use data_interface::srv::to_yaml() instead")]]
inline std::string to_yaml(const data_interface::srv::InitPandaRobot_Response & msg)
{
  return data_interface::srv::to_yaml(msg);
}

template<>
inline const char * data_type<data_interface::srv::InitPandaRobot_Response>()
{
  return "data_interface::srv::InitPandaRobot_Response";
}

template<>
inline const char * name<data_interface::srv::InitPandaRobot_Response>()
{
  return "data_interface/srv/InitPandaRobot_Response";
}

template<>
struct has_fixed_size<data_interface::srv::InitPandaRobot_Response>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<data_interface::srv::InitPandaRobot_Response>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<data_interface::srv::InitPandaRobot_Response>
  : std::true_type {};

}  // namespace rosidl_generator_traits

namespace rosidl_generator_traits
{

template<>
inline const char * data_type<data_interface::srv::InitPandaRobot>()
{
  return "data_interface::srv::InitPandaRobot";
}

template<>
inline const char * name<data_interface::srv::InitPandaRobot>()
{
  return "data_interface/srv/InitPandaRobot";
}

template<>
struct has_fixed_size<data_interface::srv::InitPandaRobot>
  : std::integral_constant<
    bool,
    has_fixed_size<data_interface::srv::InitPandaRobot_Request>::value &&
    has_fixed_size<data_interface::srv::InitPandaRobot_Response>::value
  >
{
};

template<>
struct has_bounded_size<data_interface::srv::InitPandaRobot>
  : std::integral_constant<
    bool,
    has_bounded_size<data_interface::srv::InitPandaRobot_Request>::value &&
    has_bounded_size<data_interface::srv::InitPandaRobot_Response>::value
  >
{
};

template<>
struct is_service<data_interface::srv::InitPandaRobot>
  : std::true_type
{
};

template<>
struct is_service_request<data_interface::srv::InitPandaRobot_Request>
  : std::true_type
{
};

template<>
struct is_service_response<data_interface::srv::InitPandaRobot_Response>
  : std::true_type
{
};

}  // namespace rosidl_generator_traits

#endif  // DATA_INTERFACE__SRV__DETAIL__INIT_PANDA_ROBOT__TRAITS_HPP_
