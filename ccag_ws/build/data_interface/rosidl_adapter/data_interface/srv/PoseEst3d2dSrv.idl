// generated from rosidl_adapter/resource/srv.idl.em
// with input from data_interface/srv/PoseEst3d2dSrv.srv
// generated code does not contain a copyright notice

#include "data_interface/msg/PoseEst3d2dPointsMsg.idl"
#include "geometry_msgs/msg/PoseStamped.idl"

module data_interface {
  module srv {
    @verbatim (language="comment", text=
      "PoseEst3d2dSrv.srv")
    struct PoseEst3d2dSrv_Request {
      data_interface::msg::PoseEst3d2dPointsMsg points;
    };
    struct PoseEst3d2dSrv_Response {
      geometry_msgs::msg::PoseStamped pose;
    };
  };
};
