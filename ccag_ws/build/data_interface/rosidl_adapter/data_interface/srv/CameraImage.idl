// generated from rosidl_adapter/resource/srv.idl.em
// with input from data_interface/srv/CameraImage.srv
// generated code does not contain a copyright notice

#include "sensor_msgs/msg/Image.idl"

module data_interface {
  module srv {
    struct CameraImage_Request {
      @verbatim (language="comment", text=
        "request from client")
      boolean get;
    };
    struct CameraImage_Response {
      @verbatim (language="comment", text=
        "img response from service")
      sensor_msgs::msg::Image server_image;
    };
  };
};
