<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from /opt/ros/kinetic/share/moveit_resources/panda_description/urdf/panda.urdf | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="panda" xmlns:xacro="http://www.ros.org/wiki/xacro">
    <link name="panda_link0">
        <visual>
            <geometry>
                <mesh filename="package://panda_description/meshes/visual/link0.dae" />
            </geometry>
        </visual>
        <collision>
            <geometry>
                <mesh filename="package://panda_description/meshes/collision/link0.stl" />
            </geometry>
        </collision>
    </link>
    <link name="panda_link1">
        <inertial>
          <mass value="0"/>
          <origin rpy="0 0 0" xyz="0 0 0"/>
          <inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0"/>
        </inertial>
        <visual>
            <geometry>
                <mesh filename="package://panda_description/meshes/visual/link1.dae" />
            </geometry>
        </visual>
        <collision>
            <geometry>
                <mesh filename="package://panda_description/meshes/collision/link1.stl" />
            </geometry>
        </collision>
    </link>
    <joint name="panda_joint1" type="revolute">
        <!-- <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-2.8973" soft_upper_limit="2.8973" /> -->
        <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-3.1416" soft_upper_limit="3.1416" />
        <origin rpy="0 0 0" xyz="0 0 0.333" />
        <parent link="panda_link0" />
        <child link="panda_link1" />
        <axis xyz="0 0 1" />
        <limit effort="87" lower="-3.1416" upper="3.1416" velocity="2.3925" />
    </joint>
    <link name="panda_link2">
        <inertial>
          <mass value="0"/>
          <origin rpy="0 0 0" xyz="0 0 0"/>
          <inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0"/>
        </inertial>
        <visual>
            <geometry>
                <mesh filename="package://panda_description/meshes/visual/link2.dae" />
            </geometry>
        </visual>
        <collision>
            <geometry>
                <mesh filename="package://panda_description/meshes/collision/link2.stl" />
            </geometry>
        </collision>
    </link>
    <joint name="panda_joint2" type="revolute">
        <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-1.5708" soft_upper_limit="1.5708" />
        <origin rpy="-1.57079632679 0 0" xyz="0 0 0" />
        <parent link="panda_link1" />
        <child link="panda_link2" />
        <axis xyz="0 0 1" />
        <limit effort="87" lower="-1.5708" upper="1.5708" velocity="2.3925" />
    </joint>
    <link name="panda_link3">
        <inertial>
          <mass value="0"/>
          <origin rpy="0 0 0" xyz="0 0 0"/>
          <inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0"/>
        </inertial>
        <visual>
            <geometry>
                <mesh filename="package://panda_description/meshes/visual/link3.dae" />
            </geometry>
        </visual>
        <collision>
            <geometry>
                <mesh filename="package://panda_description/meshes/collision/link3.stl" />
            </geometry>
        </collision>
    </link>
    <joint name="panda_joint3" type="revolute">
        <!-- <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-2.8973" soft_upper_limit="2.8973" /> -->
        <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-3.1416" soft_upper_limit="3.1416" />
        <origin rpy="1.57079632679 0 0" xyz="0 -0.316 0" />
        <parent link="panda_link2" />
        <child link="panda_link3" />
        <axis xyz="0 0 1" />
        <limit effort="87" lower="-3.1416" upper="3.1416" velocity="2.3925" />
    </joint>
    <link name="panda_link4">
        <inertial>
          <mass value="0"/>
          <origin rpy="0 0 0" xyz="0 0 0"/>
          <inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0"/>
        </inertial>
        <visual>
            <geometry>
                <mesh filename="package://panda_description/meshes/visual/link4.dae" />
            </geometry>
        </visual>
        <collision>
            <geometry>
                <mesh filename="package://panda_description/meshes/collision/link4.stl" />
            </geometry>
        </collision>
    </link>
    <joint name="panda_joint4" type="revolute">
        <!-- <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-3.0718" soft_upper_limit="0.0175" /> -->
        <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-3.1416" soft_upper_limit="3.1416" />
        <origin rpy="1.57079632679 0 0" xyz="0.0825 0 0" />
        <parent link="panda_link3" />
        <child link="panda_link4" />
        <axis xyz="0 0 1" />
        <limit effort="87" lower="-3.1416" upper="3.1416" velocity="2.3925" />
    </joint>
    <link name="panda_link5">
        <inertial>
          <mass value="0"/>
          <origin rpy="0 0 0" xyz="0 0 0"/>
          <inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0"/>
        </inertial>
        <visual>
            <geometry>
                <mesh filename="package://panda_description/meshes/visual/link5.dae" />
            </geometry>
        </visual>
        <collision>
            <geometry>
                <mesh filename="package://panda_description/meshes/collision/link5.stl" />
            </geometry>
        </collision>
    </link>
    <joint name="panda_joint5" type="revolute">
        <!-- <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-2.8973" soft_upper_limit="2.8973" /> -->
        <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-3.1416" soft_upper_limit="3.1416" />
        <origin rpy="-1.57079632679 0 0" xyz="-0.0825 0.384 0" />
        <parent link="panda_link4" />
        <child link="panda_link5" />
        <axis xyz="0 0 1" />
        <limit effort="12" lower="-3.1416" upper="3.1416" velocity="2.8710" />
    </joint>
    <link name="panda_link6">
        <inertial>
          <mass value="0"/>
          <origin rpy="0 0 0" xyz="0 0 0"/>
          <inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0"/>
        </inertial>
        <visual>
            <geometry>
                <mesh filename="package://panda_description/meshes/visual/link6.dae" />
            </geometry>
        </visual>
        <collision>
            <geometry>
                <mesh filename="package://panda_description/meshes/collision/link6.stl" />
            </geometry>
        </collision>
    </link>
    <joint name="panda_joint6" type="revolute">
        <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="0" soft_upper_limit="3.1416" />
        <!-- <origin rpy="1.57079632679 0 0" xyz="0 0 0" /> -->
        <origin rpy="1.57079632679 -0.7854 0" xyz="0 0 0" />
        <parent link="panda_link5" />
        <child link="panda_link6" />
        <axis xyz="0 0 1" />
        <limit effort="12" lower="0" upper="3.1416" velocity="2.8710" />
    </joint>
    <link name="panda_link7">
        <inertial>
          <mass value="0"/>
          <origin rpy="0 0 0" xyz="0 0 0"/>
          <inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0"/>
        </inertial>
        <visual>
            <geometry>
                <mesh filename="package://panda_description/meshes/visual/link7.dae" />
            </geometry>
        </visual>
        <collision>
            <geometry>
                <mesh filename="package://panda_description/meshes/collision/link7.stl" />
            </geometry>
        </collision>
    </link>
    <joint name="panda_joint7" type="revolute">
        <!-- <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-2.8973" soft_upper_limit="2.8973" /> -->
        <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-3.1416" soft_upper_limit="3.1416" />
        <!-- <origin rpy="1.57079632679 0 0" xyz="0.088 0 0" /> -->
        <origin rpy="1.57079632679 -0.7854 0" xyz="0.088 0 0" />
        <parent link="panda_link6" />
        <child link="panda_link7" />
        <axis xyz="0 0 1" />
        <limit effort="12" lower="-3.1416" upper="3.1416" velocity="2.8710" />
    </joint>
    <link name="panda_link8">
        <inertial>
          <mass value="0"/>
          <origin rpy="0 0 0" xyz="0 0 0"/>
          <inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0"/>
        </inertial>
    </link>
    <joint name="panda_joint8" type="fixed">
        <origin rpy="0 0 0" xyz="0 0 0.107" />
        <parent link="panda_link7" />
        <child link="panda_link8" />
        <axis xyz="0 0 0" />
    </joint>
    <joint name="panda_hand_joint" type="fixed">
        <parent link="panda_link8" />
        <child link="panda_hand" />
        <origin rpy="0 0 -0.785398163397" xyz="0 0 0" />
    </joint>
    <link name="panda_hand">
        <inertial>
          <mass value="0"/>
          <origin rpy="0 0 0" xyz="0 0 0"/>
          <inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0"/>
        </inertial>
        <visual>
            <geometry>
                <mesh filename="package://panda_description/meshes/visual/hand.dae" />
            </geometry>
        </visual>
        <collision>
            <geometry>
                <mesh filename="package://panda_description/meshes/collision/hand.stl" />
            </geometry>
        </collision>
    </link>
    <link name="plug">
        <inertial>
          <mass value="0"/>
          <origin rpy="0 0 0" xyz="0 0 0"/>
          <inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0"/>
        </inertial>
        <visual>
            <geometry>
                <mesh filename="package://panda_description/meshes/collision/GBT_AC_plug.stl"/>
            </geometry>
        </visual>
        <collision>
            <geometry>
                <mesh filename="package://panda_description/meshes/collision/GBT_AC_plug.stl"/>
            </geometry>
        </collision>
    </link>
    <joint name="plug_joint" type="fixed">
        <parent link="panda_hand" />
        <child link="plug" />
        <origin rpy="-1.57 0 -1.57" xyz="-0.05 0 0.15" />
    </joint>
    <link name="camera">
        <inertial>
          <mass value="0"/>
          <origin rpy="0 0 0" xyz="0 0 0"/>
          <inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0"/>
        </inertial>
        <visual>
            <geometry>
                <mesh filename="package://panda_description/meshes/collision/d405_solid.stl"/>
            </geometry>
        </visual>
        <collision>
            <geometry>
                <mesh filename="package://panda_description/meshes/collision/d405_solid.stl"/>
            </geometry>
        </collision>
    </link>
    <joint name="camera_joint" type="fixed">
        <parent link="panda_hand" />
        <child link="camera" />
        <origin rpy="0 0 0" xyz="0.05 0 0.05" />
    </joint>
    <link name="panda_leftfinger">
        <inertial>
          <mass value="0"/>
          <origin rpy="0 0 0" xyz="0 0 0"/>
          <inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0"/>
        </inertial>
        <visual>
            <geometry>
                <mesh filename="package://panda_description/meshes/visual/finger.dae" />
            </geometry>
        </visual>
        <collision>
            <geometry>
                <mesh filename="package://panda_description/meshes/collision/finger.stl" />
            </geometry>
        </collision>
    </link>
    <link name="panda_rightfinger">
        <inertial>
          <mass value="0"/>
          <origin rpy="0 0 0" xyz="0 0 0"/>
          <inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0"/>
        </inertial>
        <visual>
            <origin rpy="0 0 3.14159265359" xyz="0 0 0" />
            <geometry>
                <mesh filename="package://panda_description/meshes/visual/finger.dae" />
            </geometry>
        </visual>
        <collision>
            <origin rpy="0 0 3.14159265359" xyz="0 0 0" />
            <geometry>
                <mesh filename="package://panda_description/meshes/collision/finger.stl" />
            </geometry>
        </collision>
    </link>
    <joint name="panda_finger_joint1" type="prismatic">
        <parent link="panda_hand" />
        <child link="panda_leftfinger" />
        <origin rpy="0 0 0" xyz="0 0 0.0584" />
        <axis xyz="0 1 0" />
        <limit effort="20" lower="0.0" upper="0.04" velocity="0.2" />
    </joint>
    <joint name="panda_finger_joint2" type="prismatic">
        <parent link="panda_hand" />
        <child link="panda_rightfinger" />
        <origin rpy="0 0 0" xyz="0 0 0.0584" />
        <axis xyz="0 -1 0" />
        <limit effort="20" lower="0.0" upper="0.04" velocity="0.2" />
        <mimic joint="panda_finger_joint1" />
    </joint>
</robot>
