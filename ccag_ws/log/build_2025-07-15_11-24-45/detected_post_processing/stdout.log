[35m[1mConsolidate compiler generated dependencies of target post_processing[0m
[ 50%] Built target post_processing_test
[ 75%] [32mBuilding CXX object CMakeFiles/post_processing.dir/src/post_processing.cpp.o[0m
[100%] [32m[1mLinking CXX executable post_processing[0m
[100%] Built target post_processing
-- Install configuration: ""
-- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/detected_post_processing/post_processing
-- Set non-toolchain portion of runtime path of "/home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/detected_post_processing/post_processing" to ""
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/detected_post_processing/post_processing_test
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/config
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/config/camera_info.yaml
-- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/ament_index/resource_index/package_run_dependencies/detected_post_processing
-- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/ament_index/resource_index/parent_prefix_path/detected_post_processing
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/ament_prefix_path.dsv
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/path.sh
-- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/path.dsv
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.bash
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.sh
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.zsh
-- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.dsv
-- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/package.dsv
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/ament_index/resource_index/packages/detected_post_processing
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/cmake/detected_post_processingConfig.cmake
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/cmake/detected_post_processingConfig-version.cmake
-- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/package.xml
