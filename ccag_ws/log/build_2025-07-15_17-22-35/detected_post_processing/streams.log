[0.011s] Invoking command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing': CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib /usr/local/bin/cmake --build /home/<USER>/ccag/ccag_ws/build/detected_post_processing -- -j28 -l28
[0.050s] [35m[1mConsolidate compiler generated dependencies of target post_processing[0m
[0.065s] [ 50%] Built target post_processing_test
[0.072s] [100%] Built target post_processing
[0.083s] Invoked command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib /usr/local/bin/cmake --build /home/<USER>/ccag/ccag_ws/build/detected_post_processing -- -j28 -l28
[0.088s] Invoking command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing': CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib /usr/local/bin/cmake --install /home/<USER>/ccag/ccag_ws/build/detected_post_processing
[0.091s] -- Install configuration: ""
[0.091s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/detected_post_processing/post_processing
[0.091s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/detected_post_processing/post_processing_test
[0.091s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/config
[0.091s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/config/camera_info.yaml
[0.091s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/ament_index/resource_index/package_run_dependencies/detected_post_processing
[0.091s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/ament_index/resource_index/parent_prefix_path/detected_post_processing
[0.091s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/ament_prefix_path.sh
[0.091s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/ament_prefix_path.dsv
[0.091s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/path.sh
[0.092s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/path.dsv
[0.092s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.bash
[0.092s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.sh
[0.092s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.zsh
[0.092s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.dsv
[0.092s] -- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/package.dsv
[0.092s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/ament_index/resource_index/packages/detected_post_processing
[0.092s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/cmake/detected_post_processingConfig.cmake
[0.092s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/cmake/detected_post_processingConfig-version.cmake
[0.092s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/package.xml
[0.094s] Invoked command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib /usr/local/bin/cmake --install /home/<USER>/ccag/ccag_ws/build/detected_post_processing
