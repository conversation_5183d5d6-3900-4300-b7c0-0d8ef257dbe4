[0.443s] Invoking command in '/home/<USER>/ccag/ccag_ws/src/mujoco_sim': CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/detected_post_processing LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib PYTHONPATH=/home/<USER>/ccag/ccag_ws/build/mujoco_sim/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/mujoco_sim build --build-base /home/<USER>/ccag/ccag_ws/build/mujoco_sim/build install --record /home/<USER>/ccag/ccag_ws/build/mujoco_sim/install.log --single-version-externally-managed install_data
[0.588s] running egg_info
[0.596s] writing ../../build/mujoco_sim/mujoco_sim.egg-info/PKG-INFO
[0.596s] writing dependency_links to ../../build/mujoco_sim/mujoco_sim.egg-info/dependency_links.txt
[0.596s] writing entry points to ../../build/mujoco_sim/mujoco_sim.egg-info/entry_points.txt
[0.596s] writing requirements to ../../build/mujoco_sim/mujoco_sim.egg-info/requires.txt
[0.597s] writing top-level names to ../../build/mujoco_sim/mujoco_sim.egg-info/top_level.txt
[0.614s] reading manifest file '../../build/mujoco_sim/mujoco_sim.egg-info/SOURCES.txt'
[0.616s] writing manifest file '../../build/mujoco_sim/mujoco_sim.egg-info/SOURCES.txt'
[0.616s] running build
[0.616s] running build_py
[0.616s] copying mujoco_sim/panda_sim.py -> /home/<USER>/ccag/ccag_ws/build/mujoco_sim/build/lib/mujoco_sim
[0.616s] running install
[0.620s] running install_lib
[0.628s] copying /home/<USER>/ccag/ccag_ws/build/mujoco_sim/build/lib/mujoco_sim/panda_sim.py -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages/mujoco_sim
[0.629s] byte-compiling /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages/mujoco_sim/panda_sim.py to panda_sim.cpython-310.pyc
[0.630s] running install_data
[0.631s] running install_egg_info
[0.640s] removing '/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages/mujoco_sim-0.0.0-py3.10.egg-info' (and everything under it)
[0.640s] Copying ../../build/mujoco_sim/mujoco_sim.egg-info to /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages/mujoco_sim-0.0.0-py3.10.egg-info
[0.641s] running install_scripts
[0.731s] Installing init_panda script to /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/mujoco_sim
[0.731s] Installing panda_sim script to /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/mujoco_sim
[0.731s] writing list of installed files to '/home/<USER>/ccag/ccag_ws/build/mujoco_sim/install.log'
[0.747s] Invoked command in '/home/<USER>/ccag/ccag_ws/src/mujoco_sim' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/detected_post_processing LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib PYTHONPATH=/home/<USER>/ccag/ccag_ws/build/mujoco_sim/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/mujoco_sim build --build-base /home/<USER>/ccag/ccag_ws/build/mujoco_sim/build install --record /home/<USER>/ccag/ccag_ws/build/mujoco_sim/install.log --single-version-externally-managed install_data
