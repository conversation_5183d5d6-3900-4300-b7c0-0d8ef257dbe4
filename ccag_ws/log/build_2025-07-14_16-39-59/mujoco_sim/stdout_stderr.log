running egg_info
writing ../../build/mujoco_sim/mujoco_sim.egg-info/PKG-INFO
writing dependency_links to ../../build/mujoco_sim/mujoco_sim.egg-info/dependency_links.txt
writing entry points to ../../build/mujoco_sim/mujoco_sim.egg-info/entry_points.txt
writing requirements to ../../build/mujoco_sim/mujoco_sim.egg-info/requires.txt
writing top-level names to ../../build/mujoco_sim/mujoco_sim.egg-info/top_level.txt
reading manifest file '../../build/mujoco_sim/mujoco_sim.egg-info/SOURCES.txt'
writing manifest file '../../build/mujoco_sim/mujoco_sim.egg-info/SOURCES.txt'
running build
running build_py
copying mujoco_sim/panda_sim.py -> /home/<USER>/ccag/ccag_ws/build/mujoco_sim/build/lib/mujoco_sim
running install
running install_lib
copying /home/<USER>/ccag/ccag_ws/build/mujoco_sim/build/lib/mujoco_sim/panda_sim.py -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages/mujoco_sim
byte-compiling /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages/mujoco_sim/panda_sim.py to panda_sim.cpython-310.pyc
running install_data
running install_egg_info
removing '/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages/mujoco_sim-0.0.0-py3.10.egg-info' (and everything under it)
Copying ../../build/mujoco_sim/mujoco_sim.egg-info to /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages/mujoco_sim-0.0.0-py3.10.egg-info
running install_scripts
Installing init_panda script to /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/mujoco_sim
Installing panda_sim script to /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/mujoco_sim
writing list of installed files to '/home/<USER>/ccag/ccag_ws/build/mujoco_sim/install.log'
