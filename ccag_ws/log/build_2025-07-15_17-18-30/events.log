[0.000000] (-) TimerEvent: {}
[0.000255] (-) JobUnselected: {'identifier': 'camera_calibration'}
[0.000287] (-) JobUnselected: {'identifier': 'data_interface'}
[0.000299] (-) JobUnselected: {'identifier': 'handeye_realsense'}
[0.000308] (-) JobUnselected: {'identifier': 'motion_planning_pkg'}
[0.000316] (-) JobUnselected: {'identifier': 'mujoco_sim'}
[0.000325] (-) JobUnselected: {'identifier': 'pose_estimation'}
[0.000333] (-) JobUnselected: {'identifier': 'pose_estimation_client'}
[0.000344] (-) JobUnselected: {'identifier': 'qt_ros_test'}
[0.000354] (-) JobUnselected: {'identifier': 'yolo_bringup'}
[0.000363] (-) JobUnselected: {'identifier': 'yolo_msgs'}
[0.000372] (-) JobUnselected: {'identifier': 'yolo_ros'}
[0.000383] (detected_post_processing) JobQueued: {'identifier': 'detected_post_processing', 'dependencies': OrderedDict([('data_interface', '/home/<USER>/ccag/ccag_ws/install/data_interface')])}
[0.000590] (detected_post_processing) JobStarted: {'identifier': 'detected_post_processing'}
[0.009155] (detected_post_processing) JobProgress: {'identifier': 'detected_post_processing', 'progress': 'cmake'}
[0.010409] (detected_post_processing) JobProgress: {'identifier': 'detected_post_processing', 'progress': 'build'}
[0.010462] (detected_post_processing) Command: {'cmd': ['/usr/local/bin/cmake', '--build', '/home/<USER>/ccag/ccag_ws/build/detected_post_processing', '--', '-j28', '-l28'], 'cwd': '/home/<USER>/ccag/ccag_ws/build/detected_post_processing', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh:en_US:en'), ('USER', 'jsy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/terminator.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1973'), ('SYSTEMD_EXEC_PID', '2313'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '7382'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu.mandatory.path'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'jsy'), ('JOURNAL_STREAM', '8:30985'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu.default.path'), ('USERNAME', 'jsy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/home/<USER>/anaconda3/condabin:/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/sunshine:@/tmp/.ICE-unix/2290,unix/sunshine:/tmp/.ICE-unix/2290'), ('INVOCATION_ID', 'a23eb3d45f2e4cc39fa2e9c96e293c69'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('IBUS_DISABLE_SNOOPER', '1'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/anaconda3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:81ab141b-950f-49b1-84c2-3daf7fe4edc0'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ccag/ccag_ws/build/detected_post_processing'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/anaconda3/bin/conda'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ccag/ccag_ws/install/yolo_ros/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/handeye_realsense/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/data_interface/local/lib/python3.10/dist-packages:/home/<USER>/ccag/ccag_ws/install/camera_calibration/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble')]), 'shell': False}
[0.046286] (detected_post_processing) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target post_processing\x1b[0m\n'}
[0.060167] (detected_post_processing) StdoutLine: {'line': b'[ 50%] Built target post_processing_test\n'}
[0.064182] (detected_post_processing) StdoutLine: {'line': b'[ 75%] \x1b[32mBuilding CXX object CMakeFiles/post_processing.dir/src/post_processing.cpp.o\x1b[0m\n'}
[0.099865] (-) TimerEvent: {}
[0.200112] (-) TimerEvent: {}
[0.300486] (-) TimerEvent: {}
[0.300692] (detected_post_processing) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:20\x1b[m\x1b[K:\n'}
[0.300796] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/tf2_geometry_msgs/tf2_geometry_msgs/tf2_geometry_msgs.h:35:2:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K#warning This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wcpp\x07-Wcpp\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.300842] (detected_post_processing) StderrLine: {'line': b'   35 | #\x1b[01;35m\x1b[Kwarning\x1b[m\x1b[K This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead\n'}
[0.300876] (detected_post_processing) StderrLine: {'line': b'      |  \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[0.400654] (-) TimerEvent: {}
[0.500992] (-) TimerEvent: {}
[0.601307] (-) TimerEvent: {}
[0.701684] (-) TimerEvent: {}
[0.801992] (-) TimerEvent: {}
[0.902264] (-) TimerEvent: {}
[1.002545] (-) TimerEvent: {}
[1.102827] (-) TimerEvent: {}
[1.203128] (-) TimerEvent: {}
[1.303495] (-) TimerEvent: {}
[1.403830] (-) TimerEvent: {}
[1.504229] (-) TimerEvent: {}
[1.604593] (-) TimerEvent: {}
[1.704879] (-) TimerEvent: {}
[1.805190] (-) TimerEvent: {}
[1.905459] (-) TimerEvent: {}
[2.005782] (-) TimerEvent: {}
[2.106129] (-) TimerEvent: {}
[2.206380] (-) TimerEvent: {}
[2.306715] (-) TimerEvent: {}
[2.406955] (-) TimerEvent: {}
[2.507230] (-) TimerEvent: {}
[2.607531] (-) TimerEvent: {}
[2.707981] (-) TimerEvent: {}
[2.808247] (-) TimerEvent: {}
[2.908498] (-) TimerEvent: {}
[2.982099] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<cv::Point_<float> > DetectedPostProcessing::image_points_sort(const std::vector<cv::Vec<float, 3> >&, const Point2d&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[2.982340] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:329:9:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[KbottomLabel\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[2.982425] (detected_post_processing) StderrLine: {'line': b'  329 |     int \x1b[01;35m\x1b[KbottomLabel\x1b[m\x1b[K = 1 - topLabel;\n'}
[2.982498] (detected_post_processing) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K^~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.008735] (-) TimerEvent: {}
[3.109149] (-) TimerEvent: {}
[3.209544] (-) TimerEvent: {}
[3.309837] (-) TimerEvent: {}
[3.410168] (-) TimerEvent: {}
[3.510496] (-) TimerEvent: {}
[3.610844] (-) TimerEvent: {}
[3.711130] (-) TimerEvent: {}
[3.811399] (-) TimerEvent: {}
[3.911666] (-) TimerEvent: {}
[4.012004] (-) TimerEvent: {}
[4.112222] (-) TimerEvent: {}
[4.212465] (-) TimerEvent: {}
[4.312759] (-) TimerEvent: {}
[4.413025] (-) TimerEvent: {}
[4.513328] (-) TimerEvent: {}
[4.613739] (-) TimerEvent: {}
[4.714020] (-) TimerEvent: {}
[4.814336] (-) TimerEvent: {}
[4.914687] (-) TimerEvent: {}
[5.014971] (-) TimerEvent: {}
[5.115233] (-) TimerEvent: {}
[5.215486] (-) TimerEvent: {}
[5.315765] (-) TimerEvent: {}
[5.416038] (-) TimerEvent: {}
[5.516344] (-) TimerEvent: {}
[5.616624] (-) TimerEvent: {}
[5.716906] (-) TimerEvent: {}
[5.817161] (-) TimerEvent: {}
[5.917477] (-) TimerEvent: {}
[6.017771] (-) TimerEvent: {}
[6.118156] (-) TimerEvent: {}
[6.218490] (-) TimerEvent: {}
[6.318783] (-) TimerEvent: {}
[6.419076] (-) TimerEvent: {}
[6.519403] (-) TimerEvent: {}
[6.619833] (-) TimerEvent: {}
[6.720143] (-) TimerEvent: {}
[6.820485] (-) TimerEvent: {}
[6.920786] (-) TimerEvent: {}
[7.021173] (-) TimerEvent: {}
[7.121629] (-) TimerEvent: {}
[7.221939] (-) TimerEvent: {}
[7.322317] (-) TimerEvent: {}
[7.422586] (-) TimerEvent: {}
[7.522945] (-) TimerEvent: {}
[7.623312] (-) TimerEvent: {}
[7.723606] (-) TimerEvent: {}
[7.823909] (-) TimerEvent: {}
[7.924283] (-) TimerEvent: {}
[8.024548] (-) TimerEvent: {}
[8.124802] (-) TimerEvent: {}
[8.225105] (-) TimerEvent: {}
[8.325519] (-) TimerEvent: {}
[8.425915] (-) TimerEvent: {}
[8.526427] (-) TimerEvent: {}
[8.626689] (-) TimerEvent: {}
[8.727035] (-) TimerEvent: {}
[8.827347] (-) TimerEvent: {}
[8.927616] (-) TimerEvent: {}
[9.028005] (-) TimerEvent: {}
[9.128352] (-) TimerEvent: {}
[9.228764] (-) TimerEvent: {}
[9.329054] (-) TimerEvent: {}
[9.429324] (-) TimerEvent: {}
[9.529581] (-) TimerEvent: {}
[9.629846] (-) TimerEvent: {}
[9.730164] (-) TimerEvent: {}
[9.830439] (-) TimerEvent: {}
[9.930760] (-) TimerEvent: {}
[10.031134] (-) TimerEvent: {}
[10.131421] (-) TimerEvent: {}
[10.231747] (-) TimerEvent: {}
[10.332027] (-) TimerEvent: {}
[10.432340] (-) TimerEvent: {}
[10.532597] (-) TimerEvent: {}
[10.632854] (-) TimerEvent: {}
[10.733205] (-) TimerEvent: {}
[10.833562] (-) TimerEvent: {}
[10.933833] (-) TimerEvent: {}
[11.034057] (-) TimerEvent: {}
[11.134307] (-) TimerEvent: {}
[11.234582] (-) TimerEvent: {}
[11.334822] (-) TimerEvent: {}
[11.435148] (-) TimerEvent: {}
[11.535383] (-) TimerEvent: {}
