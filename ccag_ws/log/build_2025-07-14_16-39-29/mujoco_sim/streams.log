[0.451s] Invoking command in '/home/<USER>/ccag/ccag_ws/src/mujoco_sim': CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/detected_post_processing LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib PYTHONPATH=/home/<USER>/ccag/ccag_ws/build/mujoco_sim/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/mujoco_sim build --build-base /home/<USER>/ccag/ccag_ws/build/mujoco_sim/build install --record /home/<USER>/ccag/ccag_ws/build/mujoco_sim/install.log --single-version-externally-managed install_data
[0.604s] running egg_info
[0.613s] writing ../../build/mujoco_sim/mujoco_sim.egg-info/PKG-INFO
[0.613s] writing dependency_links to ../../build/mujoco_sim/mujoco_sim.egg-info/dependency_links.txt
[0.613s] writing entry points to ../../build/mujoco_sim/mujoco_sim.egg-info/entry_points.txt
[0.613s] writing requirements to ../../build/mujoco_sim/mujoco_sim.egg-info/requires.txt
[0.613s] writing top-level names to ../../build/mujoco_sim/mujoco_sim.egg-info/top_level.txt
[0.632s] reading manifest file '../../build/mujoco_sim/mujoco_sim.egg-info/SOURCES.txt'
[0.634s] writing manifest file '../../build/mujoco_sim/mujoco_sim.egg-info/SOURCES.txt'
[0.634s] running build
[0.634s] running build_py
[0.634s] copying mujoco_sim/panda_sim.py -> /home/<USER>/ccag/ccag_ws/build/mujoco_sim/build/lib/mujoco_sim
[0.634s] running install
[0.638s] running install_lib
[0.647s] copying /home/<USER>/ccag/ccag_ws/build/mujoco_sim/build/lib/mujoco_sim/panda_sim.py -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages/mujoco_sim
[0.647s] byte-compiling /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages/mujoco_sim/panda_sim.py to panda_sim.cpython-310.pyc
[0.648s] running install_data
[0.650s] running install_egg_info
[0.660s] removing '/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages/mujoco_sim-0.0.0-py3.10.egg-info' (and everything under it)
[0.660s] Copying ../../build/mujoco_sim/mujoco_sim.egg-info to /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages/mujoco_sim-0.0.0-py3.10.egg-info
[0.660s] running install_scripts
[0.753s] Installing init_panda script to /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/mujoco_sim
[0.753s] Installing panda_sim script to /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/mujoco_sim
[0.753s] writing list of installed files to '/home/<USER>/ccag/ccag_ws/build/mujoco_sim/install.log'
[0.769s] Invoked command in '/home/<USER>/ccag/ccag_ws/src/mujoco_sim' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/detected_post_processing LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib PYTHONPATH=/home/<USER>/ccag/ccag_ws/build/mujoco_sim/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/mujoco_sim build --build-base /home/<USER>/ccag/ccag_ws/build/mujoco_sim/build install --record /home/<USER>/ccag/ccag_ws/build/mujoco_sim/install.log --single-version-externally-managed install_data
