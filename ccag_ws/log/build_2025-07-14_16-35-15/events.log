[0.000000] (-) TimerEvent: {}
[0.000517] (-) JobUnselected: {'identifier': 'camera_calibration'}
[0.000557] (-) JobUnselected: {'identifier': 'data_interface'}
[0.000680] (-) JobUnselected: {'identifier': 'detected_post_processing'}
[0.000704] (-) JobUnselected: {'identifier': 'handeye_realsense'}
[0.000767] (-) JobUnselected: {'identifier': 'motion_planning_pkg'}
[0.000797] (-) JobUnselected: {'identifier': 'pose_estimation'}
[0.000869] (-) JobUnselected: {'identifier': 'pose_estimation_client'}
[0.000889] (-) JobUnselected: {'identifier': 'qt_ros_test'}
[0.000908] (-) JobUnselected: {'identifier': 'yolo_bringup'}
[0.000927] (-) JobUnselected: {'identifier': 'yolo_msgs'}
[0.000960] (-) JobUnselected: {'identifier': 'yolo_ros'}
[0.001013] (mujoco_sim) JobQueued: {'identifier': 'mujoco_sim', 'dependencies': OrderedDict([('data_interface', '/home/<USER>/ccag/ccag_ws/install/data_interface')])}
[0.001068] (mujoco_sim) JobStarted: {'identifier': 'mujoco_sim'}
[0.099967] (-) TimerEvent: {}
[0.200318] (-) TimerEvent: {}
[0.300587] (-) TimerEvent: {}
[0.400986] (-) TimerEvent: {}
[0.451727] (mujoco_sim) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', '../../build/mujoco_sim', 'build', '--build-base', '/home/<USER>/ccag/ccag_ws/build/mujoco_sim/build', 'install', '--record', '/home/<USER>/ccag/ccag_ws/build/mujoco_sim/install.log', '--single-version-externally-managed', 'install_data'], 'cwd': '/home/<USER>/ccag/ccag_ws/src/mujoco_sim', 'env': {'GJS_DEBUG_TOPICS': 'JS ERROR;JS LOG', 'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'zh_CN:zh:en_US:en', 'USER': 'jsy', 'LC_TIME': 'zh_CN.UTF-8', 'XDG_SESSION_TYPE': 'x11', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib', 'HOME': '/home/<USER>', 'CONDA_SHLVL': '0', 'OLDPWD': '/home/<USER>', 'DESKTOP_SESSION': 'ubuntu', 'GIO_LAUNCHED_DESKTOP_FILE': '/usr/share/applications/terminator.desktop', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'LC_MONETARY': 'zh_CN.UTF-8', 'MANAGERPID': '2018', 'SYSTEMD_EXEC_PID': '2420', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', '_CE_M': '', 'TERMINATOR_DBUS_NAME': 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f', 'GIO_LAUNCHED_DESKTOP_FILE_PID': '30788', 'MANDATORY_PATH': '/usr/share/gconf/ubuntu.mandatory.path', 'IM_CONFIG_PHASE': '1', 'COLCON_PREFIX_PATH': '/home/<USER>/ccag/ccag_ws/install', 'ROS_DISTRO': 'humble', 'GTK_IM_MODULE': 'fcitx', 'LOGNAME': 'jsy', 'JOURNAL_STREAM': '8:3552', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'DEFAULTS_PATH': '/usr/share/gconf/ubuntu.default.path', 'USERNAME': 'jsy', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', '_CE_CONDA': '', 'ROS_LOCALHOST_ONLY': '0', 'WINDOWPATH': '2', 'PATH': '/home/<USER>/anaconda3/condabin:/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin', 'SESSION_MANAGER': 'local/sunshine:@/tmp/.ICE-unix/2397,unix/sunshine:/tmp/.ICE-unix/2397', 'INVOCATION_ID': '33a0316e9a2b4d1c9e812f54a7424eb9', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'zh_CN.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': ':1', 'TERMINATOR_DBUS_PATH': '/net/tenshu/Terminator2', 'LANG': 'zh_CN.UTF-8', 'XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'IBUS_DISABLE_SNOOPER': '1', 'LC_TELEPHONE': 'zh_CN.UTF-8', 'XMODIFIERS': '@im=fcitx', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/gdm/Xauthority', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:', 'SSH_AGENT_LAUNCHER': 'gnome-keyring', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble', 'CONDA_PYTHON_EXE': '/home/<USER>/anaconda3/bin/python', 'SHELL': '/bin/bash', 'LC_NAME': 'zh_CN.UTF-8', 'TERMINATOR_UUID': 'urn:uuid:d8db6581-ce96-41b9-b4e7-ce8c5e1ae2eb', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'LC_MEASUREMENT': 'zh_CN.UTF-8', 'GPG_AGENT_INFO': '/run/user/1000/gnupg/S.gpg-agent:0:1', 'GJS_DEBUG_OUTPUT': 'stderr', 'LC_IDENTIFICATION': 'zh_CN.UTF-8', 'QT_IM_MODULE': 'fcitx', 'PWD': '/home/<USER>/ccag/ccag_ws/build/mujoco_sim', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'CONDA_EXE': '/home/<USER>/anaconda3/bin/conda', 'CLUTTER_IM_MODULE': 'fcitx', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/ccag/ccag_ws/build/mujoco_sim/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/yolo_ros/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/handeye_realsense/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/data_interface/local/lib/python3.10/dist-packages:/home/<USER>/ccag/ccag_ws/install/camera_calibration/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages', 'LC_NUMERIC': 'zh_CN.UTF-8', 'LC_PAPER': 'zh_CN.UTF-8', 'COLCON': '1', 'VTE_VERSION': '6800', 'CMAKE_PREFIX_PATH': '/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/detected_post_processing'}, 'shell': False}
[0.501196] (-) TimerEvent: {}
[0.597607] (mujoco_sim) StdoutLine: {'line': b'running egg_info\n'}
[0.601265] (-) TimerEvent: {}
[0.606169] (mujoco_sim) StdoutLine: {'line': b'writing ../../build/mujoco_sim/mujoco_sim.egg-info/PKG-INFO\n'}
[0.606292] (mujoco_sim) StdoutLine: {'line': b'writing dependency_links to ../../build/mujoco_sim/mujoco_sim.egg-info/dependency_links.txt\n'}
[0.606442] (mujoco_sim) StdoutLine: {'line': b'writing entry points to ../../build/mujoco_sim/mujoco_sim.egg-info/entry_points.txt\n'}
[0.606495] (mujoco_sim) StdoutLine: {'line': b'writing requirements to ../../build/mujoco_sim/mujoco_sim.egg-info/requires.txt\n'}
[0.606592] (mujoco_sim) StdoutLine: {'line': b'writing top-level names to ../../build/mujoco_sim/mujoco_sim.egg-info/top_level.txt\n'}
[0.625079] (mujoco_sim) StdoutLine: {'line': b"reading manifest file '../../build/mujoco_sim/mujoco_sim.egg-info/SOURCES.txt'\n"}
[0.626459] (mujoco_sim) StdoutLine: {'line': b"writing manifest file '../../build/mujoco_sim/mujoco_sim.egg-info/SOURCES.txt'\n"}
[0.626548] (mujoco_sim) StdoutLine: {'line': b'running build\n'}
[0.626618] (mujoco_sim) StdoutLine: {'line': b'running build_py\n'}
[0.626817] (mujoco_sim) StdoutLine: {'line': b'running install\n'}
[0.630733] (mujoco_sim) StdoutLine: {'line': b'running install_lib\n'}
[0.639977] (mujoco_sim) StdoutLine: {'line': b'running install_data\n'}
[0.640097] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/screenshot.png -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description\n'}
[0.641063] (mujoco_sim) StdoutLine: {'line': b'creating /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes\n'}
[0.641139] (mujoco_sim) StdoutLine: {'line': b'creating /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/collision\n'}
[0.641204] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/collision/link2.stl -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/collision\n'}
[0.641263] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/collision/link3.stl -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/collision\n'}
[0.641426] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/collision/link5.stl -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/collision\n'}
[0.641557] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/collision/GBT_AC_port.stl -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/collision\n'}
[0.645172] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/collision/link7.stl -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/collision\n'}
[0.645334] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/collision/finger.stl -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/collision\n'}
[0.645428] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/collision/hand.stl -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/collision\n'}
[0.645503] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/collision/link1.stl -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/collision\n'}
[0.645572] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/collision/link4.stl -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/collision\n'}
[0.645640] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/collision/link6.stl -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/collision\n'}
[0.645719] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/collision/GBT_AC_plug.stl -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/collision\n'}
[0.649990] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/collision/d405_solid.stl -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/collision\n'}
[0.650526] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/collision/link0.stl -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/collision\n'}
[0.650656] (mujoco_sim) StdoutLine: {'line': b'creating /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.650735] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link6_11.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.650807] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link5_0.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.651033] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link6_13.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.651123] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link0_2.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.651397] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link5_collision_1.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.651505] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link6_14.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.651676] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link0.dae -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.652425] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/hand.dae -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.652731] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link6_16.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.654668] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link0_0.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.654813] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link5_collision_0.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.654890] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link0_4.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.654974] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link6_15.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.655326] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link6_2.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.655439] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link6_12.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.655524] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link0_1.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.655610] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link3_2.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.655682] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link6_1.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.655749] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link6_10.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.655833] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link6.dae -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.656639] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link1.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.658197] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link0_11.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.658322] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link7_5.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.658400] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link4.dae -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.658826] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/finger.dae -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.658941] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link7_7.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.659238] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link3_0.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.660685] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link0_9.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.660815] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/hand_2.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.661058] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link4_2.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.661294] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link4_1.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.662858] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link1.dae -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.663248] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link3.dae -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.663755] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/hand_4.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.663901] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/hand_1.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.663991] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link7_4.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.664074] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link7_1.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.664158] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link0_8.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.665813] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link0_3.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.665914] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link5_2.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.667634] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/finger_0.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.667713] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link5_collision_2.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.667784] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link4_0.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.667884] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/hand_3.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.668143] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link5.dae -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.668911] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link0_10.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.669066] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link6_4.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.669167] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link0_5.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.669261] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link6_5.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.669342] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link7_2.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.669427] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link6_9.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.669507] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link6_3.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.669573] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link3_3.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.669676] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link7_3.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.669786] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link4_3.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.669902] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link7_6.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.669993] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link0_7.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.670089] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link6_7.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.670180] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/finger_1.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.670267] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link5_1.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.670343] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link6_0.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.670424] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link6_6.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.670506] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/hand_0.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.670585] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link7.dae -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.670948] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link6_8.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.671056] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link2.dae -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.671456] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link2.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.673070] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link3_1.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.673162] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/meshes/visual/link7_0.obj -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/meshes/visual\n'}
[0.673779] (mujoco_sim) StdoutLine: {'line': b'creating /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/mjcf\n'}
[0.673875] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/mjcf/scene.xml -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/mjcf\n'}
[0.673952] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/mjcf/panda.png -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/mjcf\n'}
[0.675015] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/mjcf/panda.xml -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/mjcf\n'}
[0.675124] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/mjcf/scene_copy.xml -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/mjcf\n'}
[0.675300] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/mjcf/panda_copy.xml -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/mjcf\n'}
[0.675371] (mujoco_sim) StdoutLine: {'line': b'creating /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/srdf\n'}
[0.675411] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/srdf/panda.srdf -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/srdf\n'}
[0.675450] (mujoco_sim) StdoutLine: {'line': b'creating /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/urdf\n'}
[0.675486] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/urdf/panda\xef\xbc\x88\xe5\xa4\x8d\xe4\xbb\xb6\xef\xbc\x89.urdf -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/urdf\n'}
[0.675545] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/urdf/panda.urdf -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/urdf\n'}
[0.675605] (mujoco_sim) StdoutLine: {'line': b'copying mujoco_sim/descriptions/panda_description/urdf/panda_copy.urdf -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description/urdf\n'}
[0.675640] (mujoco_sim) StdoutLine: {'line': b'running install_egg_info\n'}
[0.685124] (mujoco_sim) StdoutLine: {'line': b"removing '/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages/mujoco_sim-0.0.0-py3.10.egg-info' (and everything under it)\n"}
[0.685364] (mujoco_sim) StdoutLine: {'line': b'Copying ../../build/mujoco_sim/mujoco_sim.egg-info to /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages/mujoco_sim-0.0.0-py3.10.egg-info\n'}
[0.685747] (mujoco_sim) StdoutLine: {'line': b'running install_scripts\n'}
[0.701372] (-) TimerEvent: {}
[0.778356] (mujoco_sim) StdoutLine: {'line': b'Installing init_panda script to /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/mujoco_sim\n'}
[0.778649] (mujoco_sim) StdoutLine: {'line': b'Installing panda_sim script to /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/mujoco_sim\n'}
[0.778752] (mujoco_sim) StdoutLine: {'line': b"writing list of installed files to '/home/<USER>/ccag/ccag_ws/build/mujoco_sim/install.log'\n"}
[0.796482] (mujoco_sim) CommandEnded: {'returncode': 0}
[0.801660] (-) TimerEvent: {}
[0.804463] (mujoco_sim) JobEnded: {'identifier': 'mujoco_sim', 'rc': 0}
[0.804951] (-) EventReactorShutdown: {}
