[0.000000] (-) TimerEvent: {}
[0.000560] (-) JobUnselected: {'identifier': 'camera_calibration'}
[0.000724] (-) JobUnselected: {'identifier': 'data_interface'}
[0.000750] (-) JobUnselected: {'identifier': 'handeye_realsense'}
[0.000819] (-) JobUnselected: {'identifier': 'motion_planning_pkg'}
[0.000842] (-) JobUnselected: {'identifier': 'mujoco_sim'}
[0.000872] (-) JobUnselected: {'identifier': 'pose_estimation'}
[0.000893] (-) JobUnselected: {'identifier': 'pose_estimation_client'}
[0.000967] (-) JobUnselected: {'identifier': 'qt_ros_test'}
[0.000990] (-) JobUnselected: {'identifier': 'yolo_bringup'}
[0.001011] (-) JobUnselected: {'identifier': 'yolo_msgs'}
[0.001045] (-) JobUnselected: {'identifier': 'yolo_ros'}
[0.001103] (detected_post_processing) JobQueued: {'identifier': 'detected_post_processing', 'dependencies': OrderedDict([('data_interface', '/home/<USER>/ccag/ccag_ws/install/data_interface')])}
[0.001177] (detected_post_processing) JobStarted: {'identifier': 'detected_post_processing'}
[0.009584] (detected_post_processing) JobProgress: {'identifier': 'detected_post_processing', 'progress': 'cmake'}
[0.010263] (detected_post_processing) JobProgress: {'identifier': 'detected_post_processing', 'progress': 'build'}
[0.011097] (detected_post_processing) Command: {'cmd': ['/usr/local/bin/cmake', '--build', '/home/<USER>/ccag/ccag_ws/build/detected_post_processing', '--', '-j28', '-l28'], 'cwd': '/home/<USER>/ccag/ccag_ws/build/detected_post_processing', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh:en_US:en'), ('USER', 'jsy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/terminator.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1973'), ('SYSTEMD_EXEC_PID', '2313'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '7382'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu.mandatory.path'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'jsy'), ('JOURNAL_STREAM', '8:30985'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu.default.path'), ('USERNAME', 'jsy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/home/<USER>/anaconda3/condabin:/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/sunshine:@/tmp/.ICE-unix/2290,unix/sunshine:/tmp/.ICE-unix/2290'), ('INVOCATION_ID', 'a23eb3d45f2e4cc39fa2e9c96e293c69'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('IBUS_DISABLE_SNOOPER', '1'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/anaconda3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:81ab141b-950f-49b1-84c2-3daf7fe4edc0'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ccag/ccag_ws/build/detected_post_processing'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/anaconda3/bin/conda'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ccag/ccag_ws/install/yolo_ros/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/handeye_realsense/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/data_interface/local/lib/python3.10/dist-packages:/home/<USER>/ccag/ccag_ws/install/camera_calibration/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble')]), 'shell': False}
[0.051796] (detected_post_processing) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target post_processing\x1b[0m\n'}
[0.065388] (detected_post_processing) StdoutLine: {'line': b'[ 50%] Built target post_processing_test\n'}
[0.070658] (detected_post_processing) StdoutLine: {'line': b'[ 75%] \x1b[32mBuilding CXX object CMakeFiles/post_processing.dir/src/post_processing.cpp.o\x1b[0m\n'}
[0.099716] (-) TimerEvent: {}
[0.200057] (-) TimerEvent: {}
[0.297855] (detected_post_processing) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:20\x1b[m\x1b[K:\n'}
[0.298030] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/tf2_geometry_msgs/tf2_geometry_msgs/tf2_geometry_msgs.h:35:2:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K#warning This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wcpp\x07-Wcpp\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.298081] (detected_post_processing) StderrLine: {'line': b'   35 | #\x1b[01;35m\x1b[Kwarning\x1b[m\x1b[K This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead\n'}
[0.298119] (detected_post_processing) StderrLine: {'line': b'      |  \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[0.300223] (-) TimerEvent: {}
[0.400491] (-) TimerEvent: {}
[0.500835] (-) TimerEvent: {}
[0.601118] (-) TimerEvent: {}
[0.701433] (-) TimerEvent: {}
[0.801712] (-) TimerEvent: {}
[0.901980] (-) TimerEvent: {}
[1.002266] (-) TimerEvent: {}
[1.102506] (-) TimerEvent: {}
[1.202840] (-) TimerEvent: {}
[1.303147] (-) TimerEvent: {}
[1.403522] (-) TimerEvent: {}
[1.503801] (-) TimerEvent: {}
[1.604090] (-) TimerEvent: {}
[1.704372] (-) TimerEvent: {}
[1.804641] (-) TimerEvent: {}
[1.904950] (-) TimerEvent: {}
[2.005310] (-) TimerEvent: {}
[2.105603] (-) TimerEvent: {}
[2.205907] (-) TimerEvent: {}
[2.306263] (-) TimerEvent: {}
[2.406570] (-) TimerEvent: {}
[2.506943] (-) TimerEvent: {}
[2.607309] (-) TimerEvent: {}
[2.707605] (-) TimerEvent: {}
[2.807889] (-) TimerEvent: {}
[2.908240] (-) TimerEvent: {}
[2.911421] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid DetectedPostProcessing::service_response_callback(rclcpp::Client<data_interface::srv::PoseEst3d2dSrv>::SharedFuture)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[2.911567] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:195:27:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kredeclaration of \xe2\x80\x98\x1b[01m\x1b[Ktf2::Quaternion q\x1b[m\x1b[K\xe2\x80\x99\n'}
[2.911644] (detected_post_processing) StderrLine: {'line': b'  195 |           tf2::Quaternion \x1b[01;31m\x1b[Kq\x1b[m\x1b[K;\n'}
[2.911712] (detected_post_processing) StderrLine: {'line': b'      |                           \x1b[01;31m\x1b[K^\x1b[m\x1b[K\n'}
[2.911778] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:166:27:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Ktf2::Quaternion q\x1b[m\x1b[K\xe2\x80\x99 previously declared here\n'}
[2.911844] (detected_post_processing) StderrLine: {'line': b'  166 |           tf2::Quaternion \x1b[01;36m\x1b[Kq\x1b[m\x1b[K(\n'}
[2.911907] (detected_post_processing) StderrLine: {'line': b'      |                           \x1b[01;36m\x1b[K^\x1b[m\x1b[K\n'}
[2.924368] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<cv::Point_<float> > DetectedPostProcessing::image_points_sort(const std::vector<cv::Vec<float, 3> >&, const Point2d&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[2.924602] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:330:9:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[KbottomLabel\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[2.924680] (detected_post_processing) StderrLine: {'line': b'  330 |     int \x1b[01;35m\x1b[KbottomLabel\x1b[m\x1b[K = 1 - topLabel;\n'}
[2.924747] (detected_post_processing) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K^~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.008367] (-) TimerEvent: {}
[3.108647] (-) TimerEvent: {}
[3.208918] (-) TimerEvent: {}
[3.309334] (-) TimerEvent: {}
[3.409676] (-) TimerEvent: {}
[3.510076] (-) TimerEvent: {}
[3.610448] (-) TimerEvent: {}
[3.710810] (-) TimerEvent: {}
[3.811146] (-) TimerEvent: {}
[3.911551] (-) TimerEvent: {}
[4.011888] (-) TimerEvent: {}
[4.112133] (-) TimerEvent: {}
[4.212402] (-) TimerEvent: {}
[4.312687] (-) TimerEvent: {}
[4.412943] (-) TimerEvent: {}
[4.513231] (-) TimerEvent: {}
[4.613512] (-) TimerEvent: {}
[4.713784] (-) TimerEvent: {}
[4.814096] (-) TimerEvent: {}
[4.914371] (-) TimerEvent: {}
[5.014609] (-) TimerEvent: {}
[5.114876] (-) TimerEvent: {}
[5.215239] (-) TimerEvent: {}
[5.315515] (-) TimerEvent: {}
[5.415886] (-) TimerEvent: {}
[5.516234] (-) TimerEvent: {}
[5.616541] (-) TimerEvent: {}
[5.716841] (-) TimerEvent: {}
[5.817172] (-) TimerEvent: {}
[5.917477] (-) TimerEvent: {}
[6.017790] (-) TimerEvent: {}
[6.118091] (-) TimerEvent: {}
[6.218458] (-) TimerEvent: {}
[6.318853] (-) TimerEvent: {}
[6.419204] (-) TimerEvent: {}
[6.519503] (-) TimerEvent: {}
[6.541141] (detected_post_processing) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/post_processing.dir/build.make:76\xef\xbc\x9aCMakeFiles/post_processing.dir/src/post_processing.cpp.o] \xe9\x94\x99\xe8\xaf\xaf 1\n'}
[6.541456] (detected_post_processing) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:139\xef\xbc\x9aCMakeFiles/post_processing.dir/all] \xe9\x94\x99\xe8\xaf\xaf 2\n'}
[6.541714] (detected_post_processing) StderrLine: {'line': b'gmake: *** [Makefile:146\xef\xbc\x9aall] \xe9\x94\x99\xe8\xaf\xaf 2\n'}
[6.543372] (detected_post_processing) CommandEnded: {'returncode': 2}
[6.550838] (detected_post_processing) JobEnded: {'identifier': 'detected_post_processing', 'rc': 2}
[6.561525] (-) EventReactorShutdown: {}
