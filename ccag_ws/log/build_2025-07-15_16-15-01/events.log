[0.000000] (-) TimerEvent: {}
[0.000409] (-) JobUnselected: {'identifier': 'camera_calibration'}
[0.000571] (-) JobUnselected: {'identifier': 'data_interface'}
[0.000599] (-) JobUnselected: {'identifier': 'handeye_realsense'}
[0.000667] (-) JobUnselected: {'identifier': 'motion_planning_pkg'}
[0.000690] (-) JobUnselected: {'identifier': 'mujoco_sim'}
[0.000713] (-) JobUnselected: {'identifier': 'pose_estimation'}
[0.000793] (-) JobUnselected: {'identifier': 'pose_estimation_client'}
[0.000815] (-) JobUnselected: {'identifier': 'qt_ros_test'}
[0.000836] (-) JobUnselected: {'identifier': 'yolo_bringup'}
[0.000868] (-) JobUnselected: {'identifier': 'yolo_msgs'}
[0.000933] (-) JobUnselected: {'identifier': 'yolo_ros'}
[0.000960] (detected_post_processing) JobQueued: {'identifier': 'detected_post_processing', 'dependencies': OrderedDict([('data_interface', '/home/<USER>/ccag/ccag_ws/install/data_interface')])}
[0.000990] (detected_post_processing) JobStarted: {'identifier': 'detected_post_processing'}
[0.008882] (detected_post_processing) JobProgress: {'identifier': 'detected_post_processing', 'progress': 'cmake'}
[0.009495] (detected_post_processing) JobProgress: {'identifier': 'detected_post_processing', 'progress': 'build'}
[0.010187] (detected_post_processing) Command: {'cmd': ['/usr/local/bin/cmake', '--build', '/home/<USER>/ccag/ccag_ws/build/detected_post_processing', '--', '-j28', '-l28'], 'cwd': '/home/<USER>/ccag/ccag_ws/build/detected_post_processing', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh:en_US:en'), ('USER', 'jsy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/terminator.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1973'), ('SYSTEMD_EXEC_PID', '2313'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '7382'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu.mandatory.path'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'jsy'), ('JOURNAL_STREAM', '8:30985'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu.default.path'), ('USERNAME', 'jsy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/home/<USER>/anaconda3/condabin:/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/sunshine:@/tmp/.ICE-unix/2290,unix/sunshine:/tmp/.ICE-unix/2290'), ('INVOCATION_ID', 'a23eb3d45f2e4cc39fa2e9c96e293c69'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('IBUS_DISABLE_SNOOPER', '1'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/anaconda3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:81ab141b-950f-49b1-84c2-3daf7fe4edc0'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ccag/ccag_ws/build/detected_post_processing'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/anaconda3/bin/conda'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ccag/ccag_ws/install/yolo_ros/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/handeye_realsense/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/data_interface/local/lib/python3.10/dist-packages:/home/<USER>/ccag/ccag_ws/install/camera_calibration/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble')]), 'shell': False}
[0.048361] (detected_post_processing) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target post_processing\x1b[0m\n'}
[0.061827] (detected_post_processing) StdoutLine: {'line': b'[ 50%] Built target post_processing_test\n'}
[0.072151] (detected_post_processing) StdoutLine: {'line': b'[ 75%] \x1b[32mBuilding CXX object CMakeFiles/post_processing.dir/src/post_processing.cpp.o\x1b[0m\n'}
[0.099692] (-) TimerEvent: {}
[0.200064] (-) TimerEvent: {}
[0.300367] (-) TimerEvent: {}
[0.352139] (detected_post_processing) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:20\x1b[m\x1b[K:\n'}
[0.352384] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/tf2_geometry_msgs/tf2_geometry_msgs/tf2_geometry_msgs.h:35:2:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K#warning This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wcpp\x07-Wcpp\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.352471] (detected_post_processing) StderrLine: {'line': b'   35 | #\x1b[01;35m\x1b[Kwarning\x1b[m\x1b[K This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead\n'}
[0.352546] (detected_post_processing) StderrLine: {'line': b'      |  \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[0.400489] (-) TimerEvent: {}
[0.500820] (-) TimerEvent: {}
[0.601240] (-) TimerEvent: {}
[0.701582] (-) TimerEvent: {}
[0.801994] (-) TimerEvent: {}
[0.902275] (-) TimerEvent: {}
[1.002567] (-) TimerEvent: {}
[1.102805] (-) TimerEvent: {}
[1.203053] (-) TimerEvent: {}
[1.303285] (-) TimerEvent: {}
[1.403558] (-) TimerEvent: {}
[1.503999] (-) TimerEvent: {}
[1.604256] (-) TimerEvent: {}
[1.704513] (-) TimerEvent: {}
[1.804898] (-) TimerEvent: {}
[1.905151] (-) TimerEvent: {}
[2.005411] (-) TimerEvent: {}
[2.105741] (-) TimerEvent: {}
[2.206077] (-) TimerEvent: {}
[2.306357] (-) TimerEvent: {}
[2.406673] (-) TimerEvent: {}
[2.506998] (-) TimerEvent: {}
[2.607298] (-) TimerEvent: {}
[2.707598] (-) TimerEvent: {}
[2.808010] (-) TimerEvent: {}
[2.908294] (-) TimerEvent: {}
[3.008585] (-) TimerEvent: {}
[3.011742] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<cv::Point_<float> > DetectedPostProcessing::image_points_sort(const std::vector<cv::Vec<float, 3> >&, const Point2d&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[3.011906] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:330:9:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[KbottomLabel\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[3.012025] (detected_post_processing) StderrLine: {'line': b'  330 |     int \x1b[01;35m\x1b[KbottomLabel\x1b[m\x1b[K = 1 - topLabel;\n'}
[3.012128] (detected_post_processing) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K^~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.108710] (-) TimerEvent: {}
[3.208957] (-) TimerEvent: {}
[3.309255] (-) TimerEvent: {}
[3.409489] (-) TimerEvent: {}
[3.509763] (-) TimerEvent: {}
[3.610034] (-) TimerEvent: {}
[3.710289] (-) TimerEvent: {}
[3.810588] (-) TimerEvent: {}
[3.910845] (-) TimerEvent: {}
[4.011139] (-) TimerEvent: {}
[4.111401] (-) TimerEvent: {}
[4.211655] (-) TimerEvent: {}
[4.311949] (-) TimerEvent: {}
[4.412215] (-) TimerEvent: {}
[4.512466] (-) TimerEvent: {}
[4.612812] (-) TimerEvent: {}
[4.713062] (-) TimerEvent: {}
[4.813396] (-) TimerEvent: {}
[4.913650] (-) TimerEvent: {}
[5.013998] (-) TimerEvent: {}
[5.114242] (-) TimerEvent: {}
[5.214522] (-) TimerEvent: {}
[5.314928] (-) TimerEvent: {}
[5.415239] (-) TimerEvent: {}
[5.515519] (-) TimerEvent: {}
[5.615762] (-) TimerEvent: {}
[5.716051] (-) TimerEvent: {}
[5.816299] (-) TimerEvent: {}
[5.916634] (-) TimerEvent: {}
[6.016979] (-) TimerEvent: {}
[6.117281] (-) TimerEvent: {}
[6.217528] (-) TimerEvent: {}
[6.317821] (-) TimerEvent: {}
[6.418088] (-) TimerEvent: {}
[6.518510] (-) TimerEvent: {}
[6.618828] (-) TimerEvent: {}
[6.719082] (-) TimerEvent: {}
[6.819418] (-) TimerEvent: {}
[6.919801] (-) TimerEvent: {}
[7.020042] (-) TimerEvent: {}
[7.120266] (-) TimerEvent: {}
[7.220520] (-) TimerEvent: {}
[7.320786] (-) TimerEvent: {}
[7.421158] (-) TimerEvent: {}
[7.521421] (-) TimerEvent: {}
[7.621726] (-) TimerEvent: {}
[7.722054] (-) TimerEvent: {}
[7.822272] (-) TimerEvent: {}
[7.922513] (-) TimerEvent: {}
[8.022736] (-) TimerEvent: {}
[8.122931] (-) TimerEvent: {}
[8.223200] (-) TimerEvent: {}
[8.323450] (-) TimerEvent: {}
[8.423790] (-) TimerEvent: {}
[8.524049] (-) TimerEvent: {}
[8.624265] (-) TimerEvent: {}
[8.724488] (-) TimerEvent: {}
[8.824806] (-) TimerEvent: {}
[8.925139] (-) TimerEvent: {}
[9.025401] (-) TimerEvent: {}
[9.125652] (-) TimerEvent: {}
[9.225874] (-) TimerEvent: {}
[9.326200] (-) TimerEvent: {}
[9.426510] (-) TimerEvent: {}
[9.526787] (-) TimerEvent: {}
[9.627059] (-) TimerEvent: {}
[9.727298] (-) TimerEvent: {}
[9.827507] (-) TimerEvent: {}
[9.927749] (-) TimerEvent: {}
[10.028082] (-) TimerEvent: {}
[10.128293] (-) TimerEvent: {}
[10.228592] (-) TimerEvent: {}
[10.328952] (-) TimerEvent: {}
[10.429210] (-) TimerEvent: {}
[10.529478] (-) TimerEvent: {}
[10.629819] (-) TimerEvent: {}
[10.730099] (-) TimerEvent: {}
[10.830440] (-) TimerEvent: {}
