[0.000000] (-) TimerEvent: {}
[0.000520] (-) JobUnselected: {'identifier': 'camera_calibration'}
[0.000558] (-) JobUnselected: {'identifier': 'data_interface'}
[0.000581] (-) JobUnselected: {'identifier': 'detected_post_processing'}
[0.000643] (-) JobUnselected: {'identifier': 'handeye_realsense'}
[0.000664] (-) JobUnselected: {'identifier': 'motion_planning_pkg'}
[0.000685] (-) JobUnselected: {'identifier': 'pose_estimation'}
[0.000703] (-) JobUnselected: {'identifier': 'pose_estimation_client'}
[0.000765] (-) JobUnselected: {'identifier': 'qt_ros_test'}
[0.000791] (-) JobUnselected: {'identifier': 'yolo_bringup'}
[0.000833] (-) JobUnselected: {'identifier': 'yolo_msgs'}
[0.000863] (-) JobUnselected: {'identifier': 'yolo_ros'}
[0.000884] (mujoco_sim) JobQueued: {'identifier': 'mujoco_sim', 'dependencies': OrderedDict([('data_interface', '/home/<USER>/ccag/ccag_ws/install/data_interface')])}
[0.000912] (mujoco_sim) JobStarted: {'identifier': 'mujoco_sim'}
[0.099885] (-) TimerEvent: {}
[0.132617] (mujoco_sim) StderrLine: {'line': b'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/colcon_core/executor/__init__.py", line 91, in __call__\n    rc = await self.task(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/colcon_core/task/__init__.py", line 93, in __call__\n    return await task_method(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/colcon_ros/task/ament_python/build.py", line 54, in build\n    data_files = get_data_files_mapping(\n  File "/usr/lib/python3/dist-packages/colcon_core/task/python/__init__.py", line 40, in get_data_files_mapping\n    assert isinstance(sources, list)\nAssertionError\n'}
[0.133088] (mujoco_sim) JobEnded: {'identifier': 'mujoco_sim', 'rc': 1}
[0.143037] (-) EventReactorShutdown: {}
