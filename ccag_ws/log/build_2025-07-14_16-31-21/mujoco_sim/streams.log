[0.445s] Invoking command in '/home/<USER>/ccag/ccag_ws/src/mujoco_sim': CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/detected_post_processing LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib PYTHONPATH=/home/<USER>/ccag/ccag_ws/build/mujoco_sim/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/mujoco_sim build --build-base /home/<USER>/ccag/ccag_ws/build/mujoco_sim/build install --record /home/<USER>/ccag/ccag_ws/build/mujoco_sim/install.log --single-version-externally-managed install_data
[0.595s] running egg_info
[0.604s] writing ../../build/mujoco_sim/mujoco_sim.egg-info/PKG-INFO
[0.604s] writing dependency_links to ../../build/mujoco_sim/mujoco_sim.egg-info/dependency_links.txt
[0.604s] writing entry points to ../../build/mujoco_sim/mujoco_sim.egg-info/entry_points.txt
[0.604s] writing requirements to ../../build/mujoco_sim/mujoco_sim.egg-info/requires.txt
[0.604s] writing top-level names to ../../build/mujoco_sim/mujoco_sim.egg-info/top_level.txt
[0.622s] reading manifest file '../../build/mujoco_sim/mujoco_sim.egg-info/SOURCES.txt'
[0.624s] writing manifest file '../../build/mujoco_sim/mujoco_sim.egg-info/SOURCES.txt'
[0.624s] running build
[0.624s] running build_py
[0.624s] running install
[0.627s] running install_lib
[0.636s] running install_data
[0.636s] creating /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions
[0.637s] creating /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description
[0.637s] error: can't copy 'mujoco_sim/descriptions/panda_description/urdf': doesn't exist or not a regular file
[0.648s] Invoked command in '/home/<USER>/ccag/ccag_ws/src/mujoco_sim' returned '1': CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/detected_post_processing LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib PYTHONPATH=/home/<USER>/ccag/ccag_ws/build/mujoco_sim/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/mujoco_sim build --build-base /home/<USER>/ccag/ccag_ws/build/mujoco_sim/build install --record /home/<USER>/ccag/ccag_ws/build/mujoco_sim/install.log --single-version-externally-managed install_data
