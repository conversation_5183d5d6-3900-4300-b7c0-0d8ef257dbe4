running egg_info
writing ../../build/mujoco_sim/mujoco_sim.egg-info/PKG-INFO
writing dependency_links to ../../build/mujoco_sim/mujoco_sim.egg-info/dependency_links.txt
writing entry points to ../../build/mujoco_sim/mujoco_sim.egg-info/entry_points.txt
writing requirements to ../../build/mujoco_sim/mujoco_sim.egg-info/requires.txt
writing top-level names to ../../build/mujoco_sim/mujoco_sim.egg-info/top_level.txt
reading manifest file '../../build/mujoco_sim/mujoco_sim.egg-info/SOURCES.txt'
writing manifest file '../../build/mujoco_sim/mujoco_sim.egg-info/SOURCES.txt'
running build
running build_py
running install
running install_lib
running install_data
creating /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions
creating /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/descriptions/panda_description
error: can't copy 'mujoco_sim/descriptions/panda_description/urdf': doesn't exist or not a regular file
