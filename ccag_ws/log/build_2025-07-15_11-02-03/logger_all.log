[0.066s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'detected_post_processing']
[0.066s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=28, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['detected_post_processing'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7c6d7164e8c0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7c6d71743ca0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7c6d71743ca0>>)
[0.089s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.089s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.089s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.089s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.089s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.089s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.089s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ccag/ccag_ws'
[0.089s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.089s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.089s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.089s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.089s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.089s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.089s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.089s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.089s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.099s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.099s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.099s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.099s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.099s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.099s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.099s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.099s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.099s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.099s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extensions ['ignore', 'ignore_ament_install']
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extension 'ignore'
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extension 'ignore_ament_install'
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extensions ['colcon_pkg']
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extension 'colcon_pkg'
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extensions ['colcon_meta']
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extension 'colcon_meta'
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extensions ['ros']
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extension 'ros'
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extensions ['cmake', 'python']
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extension 'cmake'
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extension 'python'
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extensions ['python_setup_py']
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extension 'python_setup_py'
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(src/data_interface) by extensions ['ignore', 'ignore_ament_install']
[0.100s] Level 1:colcon.colcon_core.package_identification:_identify(src/data_interface) by extension 'ignore'
[0.101s] Level 1:colcon.colcon_core.package_identification:_identify(src/data_interface) by extension 'ignore_ament_install'
[0.101s] Level 1:colcon.colcon_core.package_identification:_identify(src/data_interface) by extensions ['colcon_pkg']
[0.101s] Level 1:colcon.colcon_core.package_identification:_identify(src/data_interface) by extension 'colcon_pkg'
[0.101s] Level 1:colcon.colcon_core.package_identification:_identify(src/data_interface) by extensions ['colcon_meta']
[0.101s] Level 1:colcon.colcon_core.package_identification:_identify(src/data_interface) by extension 'colcon_meta'
[0.101s] Level 1:colcon.colcon_core.package_identification:_identify(src/data_interface) by extensions ['ros']
[0.101s] Level 1:colcon.colcon_core.package_identification:_identify(src/data_interface) by extension 'ros'
[0.102s] DEBUG:colcon.colcon_core.package_identification:Package 'src/data_interface' with type 'ros.ament_cmake' and name 'data_interface'
[0.102s] Level 1:colcon.colcon_core.package_identification:_identify(src/detected_post_processing) by extensions ['ignore', 'ignore_ament_install']
[0.102s] Level 1:colcon.colcon_core.package_identification:_identify(src/detected_post_processing) by extension 'ignore'
[0.102s] Level 1:colcon.colcon_core.package_identification:_identify(src/detected_post_processing) by extension 'ignore_ament_install'
[0.102s] Level 1:colcon.colcon_core.package_identification:_identify(src/detected_post_processing) by extensions ['colcon_pkg']
[0.102s] Level 1:colcon.colcon_core.package_identification:_identify(src/detected_post_processing) by extension 'colcon_pkg'
[0.103s] Level 1:colcon.colcon_core.package_identification:_identify(src/detected_post_processing) by extensions ['colcon_meta']
[0.103s] Level 1:colcon.colcon_core.package_identification:_identify(src/detected_post_processing) by extension 'colcon_meta'
[0.103s] Level 1:colcon.colcon_core.package_identification:_identify(src/detected_post_processing) by extensions ['ros']
[0.103s] Level 1:colcon.colcon_core.package_identification:_identify(src/detected_post_processing) by extension 'ros'
[0.103s] DEBUG:colcon.colcon_core.package_identification:Package 'src/detected_post_processing' with type 'ros.ament_cmake' and name 'detected_post_processing'
[0.103s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.103s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extension 'ignore'
[0.103s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extension 'ignore_ament_install'
[0.103s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extensions ['colcon_pkg']
[0.103s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extension 'colcon_pkg'
[0.103s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extensions ['colcon_meta']
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extension 'colcon_meta'
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extensions ['ros']
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extension 'ros'
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extensions ['cmake', 'python']
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extension 'cmake'
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extension 'python'
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extensions ['python_setup_py']
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extension 'python_setup_py'
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/camera_calibration) by extensions ['ignore', 'ignore_ament_install']
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/camera_calibration) by extension 'ignore'
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/camera_calibration) by extension 'ignore_ament_install'
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/camera_calibration) by extensions ['colcon_pkg']
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/camera_calibration) by extension 'colcon_pkg'
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/camera_calibration) by extensions ['colcon_meta']
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/camera_calibration) by extension 'colcon_meta'
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/camera_calibration) by extensions ['ros']
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/camera_calibration) by extension 'ros'
[0.104s] DEBUG:colcon.colcon_core.package_identification:Package 'src/handeye_calibration_ros2/camera_calibration' with type 'ros.ament_python' and name 'camera_calibration'
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extensions ['ignore', 'ignore_ament_install']
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extension 'ignore'
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extension 'ignore_ament_install'
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extensions ['colcon_pkg']
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extension 'colcon_pkg'
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extensions ['colcon_meta']
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extension 'colcon_meta'
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extensions ['ros']
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extension 'ros'
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extensions ['cmake', 'python']
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extension 'cmake'
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extension 'python'
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extensions ['python_setup_py']
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extension 'python_setup_py'
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/handeye_realsense) by extensions ['ignore', 'ignore_ament_install']
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/handeye_realsense) by extension 'ignore'
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/handeye_realsense) by extension 'ignore_ament_install'
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/handeye_realsense) by extensions ['colcon_pkg']
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/handeye_realsense) by extension 'colcon_pkg'
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/handeye_realsense) by extensions ['colcon_meta']
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/handeye_realsense) by extension 'colcon_meta'
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/handeye_realsense) by extensions ['ros']
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/handeye_realsense) by extension 'ros'
[0.105s] DEBUG:colcon.colcon_core.package_identification:Package 'src/handeye_calibration_ros2/handeye_realsense' with type 'ros.ament_python' and name 'handeye_realsense'
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src/motion_planning_pkg) by extensions ['ignore', 'ignore_ament_install']
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src/motion_planning_pkg) by extension 'ignore'
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src/motion_planning_pkg) by extension 'ignore_ament_install'
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src/motion_planning_pkg) by extensions ['colcon_pkg']
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src/motion_planning_pkg) by extension 'colcon_pkg'
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src/motion_planning_pkg) by extensions ['colcon_meta']
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src/motion_planning_pkg) by extension 'colcon_meta'
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src/motion_planning_pkg) by extensions ['ros']
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src/motion_planning_pkg) by extension 'ros'
[0.106s] DEBUG:colcon.colcon_core.package_identification:Package 'src/motion_planning_pkg' with type 'ros.ament_python' and name 'motion_planning_pkg'
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src/mujoco_sim) by extensions ['ignore', 'ignore_ament_install']
[0.107s] Level 1:colcon.colcon_core.package_identification:_identify(src/mujoco_sim) by extension 'ignore'
[0.107s] Level 1:colcon.colcon_core.package_identification:_identify(src/mujoco_sim) by extension 'ignore_ament_install'
[0.107s] Level 1:colcon.colcon_core.package_identification:_identify(src/mujoco_sim) by extensions ['colcon_pkg']
[0.107s] Level 1:colcon.colcon_core.package_identification:_identify(src/mujoco_sim) by extension 'colcon_pkg'
[0.107s] Level 1:colcon.colcon_core.package_identification:_identify(src/mujoco_sim) by extensions ['colcon_meta']
[0.107s] Level 1:colcon.colcon_core.package_identification:_identify(src/mujoco_sim) by extension 'colcon_meta'
[0.107s] Level 1:colcon.colcon_core.package_identification:_identify(src/mujoco_sim) by extensions ['ros']
[0.107s] Level 1:colcon.colcon_core.package_identification:_identify(src/mujoco_sim) by extension 'ros'
[0.107s] DEBUG:colcon.colcon_core.package_identification:Package 'src/mujoco_sim' with type 'ros.ament_python' and name 'mujoco_sim'
[0.107s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation) by extensions ['ignore', 'ignore_ament_install']
[0.107s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation) by extension 'ignore'
[0.107s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation) by extension 'ignore_ament_install'
[0.107s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation) by extensions ['colcon_pkg']
[0.107s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation) by extension 'colcon_pkg'
[0.107s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation) by extensions ['colcon_meta']
[0.107s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation) by extension 'colcon_meta'
[0.107s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation) by extensions ['ros']
[0.107s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation) by extension 'ros'
[0.108s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pose_estimation' with type 'ros.ament_cmake' and name 'pose_estimation'
[0.108s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation_client) by extensions ['ignore', 'ignore_ament_install']
[0.108s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation_client) by extension 'ignore'
[0.108s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation_client) by extension 'ignore_ament_install'
[0.108s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation_client) by extensions ['colcon_pkg']
[0.108s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation_client) by extension 'colcon_pkg'
[0.108s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation_client) by extensions ['colcon_meta']
[0.108s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation_client) by extension 'colcon_meta'
[0.108s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation_client) by extensions ['ros']
[0.108s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation_client) by extension 'ros'
[0.109s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pose_estimation_client' with type 'ros.ament_cmake' and name 'pose_estimation_client'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/qt_ros_test) by extensions ['ignore', 'ignore_ament_install']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/qt_ros_test) by extension 'ignore'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/qt_ros_test) by extension 'ignore_ament_install'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/qt_ros_test) by extensions ['colcon_pkg']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/qt_ros_test) by extension 'colcon_pkg'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/qt_ros_test) by extensions ['colcon_meta']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/qt_ros_test) by extension 'colcon_meta'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/qt_ros_test) by extensions ['ros']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/qt_ros_test) by extension 'ros'
[0.109s] DEBUG:colcon.colcon_core.package_identification:Package 'src/qt_ros_test' with type 'ros.ament_cmake' and name 'qt_ros_test'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extensions ['ignore', 'ignore_ament_install']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extension 'ignore'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extension 'ignore_ament_install'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extensions ['colcon_pkg']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extension 'colcon_pkg'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extensions ['colcon_meta']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extension 'colcon_meta'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extensions ['ros']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extension 'ros'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extensions ['cmake', 'python']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extension 'cmake'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extension 'python'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extensions ['python_setup_py']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extension 'python_setup_py'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extensions ['ignore', 'ignore_ament_install']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extension 'ignore'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extension 'ignore_ament_install'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extensions ['colcon_pkg']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extension 'colcon_pkg'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extensions ['colcon_meta']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extension 'colcon_meta'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extensions ['ros']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extension 'ros'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extensions ['cmake', 'python']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extension 'cmake'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extension 'python'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extensions ['python_setup_py']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extension 'python_setup_py'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_bringup) by extensions ['ignore', 'ignore_ament_install']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_bringup) by extension 'ignore'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_bringup) by extension 'ignore_ament_install'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_bringup) by extensions ['colcon_pkg']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_bringup) by extension 'colcon_pkg'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_bringup) by extensions ['colcon_meta']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_bringup) by extension 'colcon_meta'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_bringup) by extensions ['ros']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_bringup) by extension 'ros'
[0.111s] DEBUG:colcon.colcon_core.package_identification:Package 'src/yolo_ros/yolo_bringup' with type 'ros.ament_cmake' and name 'yolo_bringup'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_msgs) by extension 'ignore'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_msgs) by extension 'ignore_ament_install'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_msgs) by extensions ['colcon_pkg']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_msgs) by extension 'colcon_pkg'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_msgs) by extensions ['colcon_meta']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_msgs) by extension 'colcon_meta'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_msgs) by extensions ['ros']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_msgs) by extension 'ros'
[0.112s] DEBUG:colcon.colcon_core.package_identification:Package 'src/yolo_ros/yolo_msgs' with type 'ros.ament_cmake' and name 'yolo_msgs'
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_ros) by extensions ['ignore', 'ignore_ament_install']
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_ros) by extension 'ignore'
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_ros) by extension 'ignore_ament_install'
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_ros) by extensions ['colcon_pkg']
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_ros) by extension 'colcon_pkg'
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_ros) by extensions ['colcon_meta']
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_ros) by extension 'colcon_meta'
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_ros) by extensions ['ros']
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_ros) by extension 'ros'
[0.112s] DEBUG:colcon.colcon_core.package_identification:Package 'src/yolo_ros/yolo_ros' with type 'ros.ament_python' and name 'yolo_ros'
[0.112s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.112s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.112s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.112s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.112s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.128s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'camera_calibration' in 'src/handeye_calibration_ros2/camera_calibration'
[0.128s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'data_interface' in 'src/data_interface'
[0.128s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'handeye_realsense' in 'src/handeye_calibration_ros2/handeye_realsense'
[0.128s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'motion_planning_pkg' in 'src/motion_planning_pkg'
[0.128s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'pose_estimation_client' in 'src/pose_estimation_client'
[0.128s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'qt_ros_test' in 'src/qt_ros_test'
[0.128s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'yolo_msgs' in 'src/yolo_ros/yolo_msgs'
[0.128s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'mujoco_sim' in 'src/mujoco_sim'
[0.128s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'pose_estimation' in 'src/pose_estimation'
[0.128s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'yolo_ros' in 'src/yolo_ros/yolo_ros'
[0.128s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'yolo_bringup' in 'src/yolo_ros/yolo_bringup'
[0.128s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.128s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.130s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 12 installed packages in /home/<USER>/ccag/ccag_ws/install
[0.130s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 277 installed packages in /opt/ros/humble
[0.131s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.153s] Level 5:colcon.colcon_core.verb:set package 'detected_post_processing' build argument 'cmake_args' from command line to 'None'
[0.153s] Level 5:colcon.colcon_core.verb:set package 'detected_post_processing' build argument 'cmake_target' from command line to 'None'
[0.153s] Level 5:colcon.colcon_core.verb:set package 'detected_post_processing' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.153s] Level 5:colcon.colcon_core.verb:set package 'detected_post_processing' build argument 'cmake_clean_cache' from command line to 'False'
[0.153s] Level 5:colcon.colcon_core.verb:set package 'detected_post_processing' build argument 'cmake_clean_first' from command line to 'False'
[0.154s] Level 5:colcon.colcon_core.verb:set package 'detected_post_processing' build argument 'cmake_force_configure' from command line to 'False'
[0.154s] Level 5:colcon.colcon_core.verb:set package 'detected_post_processing' build argument 'ament_cmake_args' from command line to 'None'
[0.154s] Level 5:colcon.colcon_core.verb:set package 'detected_post_processing' build argument 'catkin_cmake_args' from command line to 'None'
[0.154s] Level 5:colcon.colcon_core.verb:set package 'detected_post_processing' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.154s] DEBUG:colcon.colcon_core.verb:Building package 'detected_post_processing' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ccag/ccag_ws/build/detected_post_processing', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ccag/ccag_ws/install/detected_post_processing', 'merge_install': False, 'path': '/home/<USER>/ccag/ccag_ws/src/detected_post_processing', 'symlink_install': False, 'test_result_base': None}
[0.154s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.155s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.155s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ccag/ccag_ws/src/detected_post_processing' with build type 'ament_cmake'
[0.155s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ccag/ccag_ws/src/detected_post_processing'
[0.156s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.156s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.156s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.165s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing': CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib /usr/local/bin/cmake --build /home/<USER>/ccag/ccag_ws/build/detected_post_processing -- -j28 -l28
[7.247s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing' returned '2': CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib /usr/local/bin/cmake --build /home/<USER>/ccag/ccag_ws/build/detected_post_processing -- -j28 -l28
[7.248s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(detected_post_processing)
[7.249s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ccag/ccag_ws/install/detected_post_processing' for CMake module files
[7.250s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ccag/ccag_ws/install/detected_post_processing' for CMake config files
[7.250s] Level 1:colcon.colcon_core.shell:create_environment_hook('detected_post_processing', 'cmake_prefix_path')
[7.250s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/hook/cmake_prefix_path.ps1'
[7.251s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/hook/cmake_prefix_path.dsv'
[7.251s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/hook/cmake_prefix_path.sh'
[7.251s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib'
[7.252s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ccag/ccag_ws/install/detected_post_processing/bin'
[7.252s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/pkgconfig/detected_post_processing.pc'
[7.252s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/python3.10/site-packages'
[7.252s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ccag/ccag_ws/install/detected_post_processing/bin'
[7.252s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/package.ps1'
[7.253s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/package.dsv'
[7.253s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/package.sh'
[7.253s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/package.bash'
[7.254s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/package.zsh'
[7.254s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/colcon-core/packages/detected_post_processing)
[7.265s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[7.265s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[7.265s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '2'
[7.265s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[7.271s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[7.271s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[7.271s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[7.286s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[7.287s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ccag/ccag_ws/install/local_setup.ps1'
[7.288s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ccag/ccag_ws/install/_local_setup_util_ps1.py'
[7.289s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ccag/ccag_ws/install/setup.ps1'
[7.290s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ccag/ccag_ws/install/local_setup.sh'
[7.291s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ccag/ccag_ws/install/_local_setup_util_sh.py'
[7.291s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ccag/ccag_ws/install/setup.sh'
[7.293s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ccag/ccag_ws/install/local_setup.bash'
[7.293s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ccag/ccag_ws/install/setup.bash'
[7.294s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ccag/ccag_ws/install/local_setup.zsh'
[7.295s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ccag/ccag_ws/install/setup.zsh'
