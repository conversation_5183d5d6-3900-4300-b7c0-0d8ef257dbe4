[0.000000] (-) TimerEvent: {}
[0.000300] (-) JobUnselected: {'identifier': 'camera_calibration'}
[0.000392] (-) JobUnselected: {'identifier': 'data_interface'}
[0.000420] (-) JobUnselected: {'identifier': 'handeye_realsense'}
[0.000440] (-) JobUnselected: {'identifier': 'motion_planning_pkg'}
[0.000459] (-) JobUnselected: {'identifier': 'mujoco_sim'}
[0.000470] (-) JobUnselected: {'identifier': 'pose_estimation'}
[0.000479] (-) JobUnselected: {'identifier': 'pose_estimation_client'}
[0.000491] (-) JobUnselected: {'identifier': 'qt_ros_test'}
[0.000510] (-) JobUnselected: {'identifier': 'yolo_bringup'}
[0.000520] (-) JobUnselected: {'identifier': 'yolo_msgs'}
[0.000529] (-) JobUnselected: {'identifier': 'yolo_ros'}
[0.000540] (detected_post_processing) JobQueued: {'identifier': 'detected_post_processing', 'dependencies': OrderedDict([('data_interface', '/home/<USER>/ccag/ccag_ws/install/data_interface')])}
[0.000584] (detected_post_processing) JobStarted: {'identifier': 'detected_post_processing'}
[0.008954] (detected_post_processing) JobProgress: {'identifier': 'detected_post_processing', 'progress': 'cmake'}
[0.009523] (detected_post_processing) JobProgress: {'identifier': 'detected_post_processing', 'progress': 'build'}
[0.010229] (detected_post_processing) Command: {'cmd': ['/usr/local/bin/cmake', '--build', '/home/<USER>/ccag/ccag_ws/build/detected_post_processing', '--', '-j28', '-l28'], 'cwd': '/home/<USER>/ccag/ccag_ws/build/detected_post_processing', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh:en_US:en'), ('USER', 'jsy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/terminator.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1973'), ('SYSTEMD_EXEC_PID', '2313'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '7382'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu.mandatory.path'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'jsy'), ('JOURNAL_STREAM', '8:30985'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu.default.path'), ('USERNAME', 'jsy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/home/<USER>/anaconda3/condabin:/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/sunshine:@/tmp/.ICE-unix/2290,unix/sunshine:/tmp/.ICE-unix/2290'), ('INVOCATION_ID', 'a23eb3d45f2e4cc39fa2e9c96e293c69'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('IBUS_DISABLE_SNOOPER', '1'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/anaconda3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:81ab141b-950f-49b1-84c2-3daf7fe4edc0'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ccag/ccag_ws/build/detected_post_processing'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/anaconda3/bin/conda'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ccag/ccag_ws/install/yolo_ros/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/handeye_realsense/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/data_interface/local/lib/python3.10/dist-packages:/home/<USER>/ccag/ccag_ws/install/camera_calibration/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble')]), 'shell': False}
[0.034204] (detected_post_processing) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.099811] (-) TimerEvent: {}
[0.145110] (detected_post_processing) StdoutLine: {'line': b'-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.172585] (detected_post_processing) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.174835] (detected_post_processing) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.180139] (detected_post_processing) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.189096] (detected_post_processing) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.199939] (-) TimerEvent: {}
[0.201379] (detected_post_processing) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.233559] (detected_post_processing) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.235162] (detected_post_processing) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.300064] (-) TimerEvent: {}
[0.317580] (detected_post_processing) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.361182] (detected_post_processing) StdoutLine: {'line': b'-- Found sensor_msgs: 4.8.0 (/opt/ros/humble/share/sensor_msgs/cmake)\n'}
[0.384265] (detected_post_processing) StdoutLine: {'line': b'-- Found vision_msgs: 4.1.1 (/opt/ros/humble/share/vision_msgs/cmake)\n'}
[0.395542] (detected_post_processing) StdoutLine: {'line': b'-- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)\n'}
[0.400174] (-) TimerEvent: {}
[0.411120] (detected_post_processing) StdoutLine: {'line': b'-- Found message_filters: 4.3.7 (/opt/ros/humble/share/message_filters/cmake)\n'}
[0.419793] (detected_post_processing) StdoutLine: {'line': b'-- Found tf2: 0.25.12 (/opt/ros/humble/share/tf2/cmake)\n'}
[0.424798] (detected_post_processing) StdoutLine: {'line': b'-- Found tf2_ros: 0.25.12 (/opt/ros/humble/share/tf2_ros/cmake)\n'}
[0.492215] (detected_post_processing) StdoutLine: {'line': b'-- Found tf2_geometry_msgs: 0.25.12 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)\n'}
[0.497178] (detected_post_processing) StdoutLine: {'line': b'-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)\n'}
[0.498350] (detected_post_processing) StdoutLine: {'line': b'-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target\n'}
[0.499423] (detected_post_processing) StdoutLine: {'line': b'-- Found data_interface: 0.0.0 (/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake)\n'}
[0.500229] (-) TimerEvent: {}
[0.513621] (detected_post_processing) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[0.575021] (detected_post_processing) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[0.575306] (detected_post_processing) StdoutLine: {'line': b'-- Configured cppcheck include dirs: \n'}
[0.575353] (detected_post_processing) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[0.575870] (detected_post_processing) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[0.576395] (detected_post_processing) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[0.576494] (detected_post_processing) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[0.576705] (detected_post_processing) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[0.577218] (detected_post_processing) StdoutLine: {'line': b'-- Configuring done\n'}
[0.592696] (detected_post_processing) StdoutLine: {'line': b'-- Generating done\n'}
[0.597677] (detected_post_processing) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/ccag/ccag_ws/build/detected_post_processing\n'}
[0.600358] (-) TimerEvent: {}
[0.620703] (detected_post_processing) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target post_processing\x1b[0m\n'}
[0.621153] (detected_post_processing) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target post_processing_test\x1b[0m\n'}
[0.640356] (detected_post_processing) StdoutLine: {'line': b'[ 25%] \x1b[32mBuilding CXX object CMakeFiles/post_processing.dir/src/post_processing.cpp.o\x1b[0m\n'}
[0.641328] (detected_post_processing) StdoutLine: {'line': b'[ 75%] Built target post_processing_test\n'}
[0.700463] (-) TimerEvent: {}
[0.800763] (-) TimerEvent: {}
[0.868550] (detected_post_processing) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:20\x1b[m\x1b[K:\n'}
[0.868774] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/tf2_geometry_msgs/tf2_geometry_msgs/tf2_geometry_msgs.h:35:2:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K#warning This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wcpp\x07-Wcpp\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.868921] (detected_post_processing) StderrLine: {'line': b'   35 | #\x1b[01;35m\x1b[Kwarning\x1b[m\x1b[K This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead\n'}
[0.868993] (detected_post_processing) StderrLine: {'line': b'      |  \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[0.900925] (-) TimerEvent: {}
[1.001196] (-) TimerEvent: {}
[1.101457] (-) TimerEvent: {}
[1.201716] (-) TimerEvent: {}
[1.302056] (-) TimerEvent: {}
[1.402426] (-) TimerEvent: {}
[1.502678] (-) TimerEvent: {}
[1.602947] (-) TimerEvent: {}
[1.703211] (-) TimerEvent: {}
[1.803529] (-) TimerEvent: {}
[1.903839] (-) TimerEvent: {}
[2.004223] (-) TimerEvent: {}
[2.104610] (-) TimerEvent: {}
[2.204887] (-) TimerEvent: {}
[2.305138] (-) TimerEvent: {}
[2.405482] (-) TimerEvent: {}
[2.505729] (-) TimerEvent: {}
[2.606003] (-) TimerEvent: {}
[2.706330] (-) TimerEvent: {}
[2.806652] (-) TimerEvent: {}
[2.906920] (-) TimerEvent: {}
[3.007220] (-) TimerEvent: {}
[3.107474] (-) TimerEvent: {}
[3.207814] (-) TimerEvent: {}
[3.308125] (-) TimerEvent: {}
[3.408374] (-) TimerEvent: {}
[3.410226] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<cv::Point_<float> > DetectedPostProcessing::image_points_sort(const std::vector<cv::Vec<float, 3> >&, const Point2d&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[3.410350] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:237:9:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[KbottomLabel\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[3.410393] (detected_post_processing) StderrLine: {'line': b'  237 |     int \x1b[01;35m\x1b[KbottomLabel\x1b[m\x1b[K = 1 - topLabel;\n'}
[3.410428] (detected_post_processing) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K^~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.508474] (-) TimerEvent: {}
[3.579794] (detected_post_processing) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155\x1b[m\x1b[K,\n'}
[3.579941] (detected_post_processing) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:1\x1b[m\x1b[K:\n'}
[3.579998] (detected_post_processing) StderrLine: {'line': b'/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp: In instantiation of \xe2\x80\x98\x1b[01m\x1b[Krclcpp::FutureReturnCode rclcpp::spin_until_future_complete(std::shared_ptr<_Tp>, const FutureT&, std::chrono::duration<_Rep2, _Period2>) [with NodeT = rclcpp::Client<data_interface::srv::PoseEst3d2dSrv>; FutureT = std::shared_future<std::shared_ptr<data_interface::srv::PoseEst3d2dSrv_Response_<std::allocator<void> > > >; TimeRepT = long int; TimeT = std::ratio<1, 1000>]\x1b[m\x1b[K\xe2\x80\x99:\n'}
[3.580044] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:454:45:\x1b[m\x1b[K   required from here\n'}
[3.580081] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:124:55:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kusing element_type = class rclcpp::Client<data_interface::srv::PoseEst3d2dSrv>\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Kclass rclcpp::Client<data_interface::srv::PoseEst3d2dSrv>\x1b[m\x1b[K\xe2\x80\x99} has no member named \xe2\x80\x98\x1b[01m\x1b[Kget_node_base_interface\x1b[m\x1b[K\xe2\x80\x99\n'}
[3.580119] (detected_post_processing) StderrLine: {'line': b'  124 |   return rclcpp::spin_until_future_complete(\x1b[01;31m\x1b[Knode_ptr->get_node_base_interface\x1b[m\x1b[K(), future, timeout);\n'}
[3.580155] (detected_post_processing) StderrLine: {'line': b'      |                                             \x1b[01;31m\x1b[K~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.608568] (-) TimerEvent: {}
[3.708826] (-) TimerEvent: {}
[3.809113] (-) TimerEvent: {}
[3.909452] (-) TimerEvent: {}
[4.009695] (-) TimerEvent: {}
[4.109976] (-) TimerEvent: {}
[4.210308] (-) TimerEvent: {}
[4.310577] (-) TimerEvent: {}
[4.410798] (-) TimerEvent: {}
[4.511071] (-) TimerEvent: {}
[4.611335] (-) TimerEvent: {}
[4.711745] (-) TimerEvent: {}
[4.812032] (-) TimerEvent: {}
[4.912321] (-) TimerEvent: {}
[5.012577] (-) TimerEvent: {}
[5.112914] (-) TimerEvent: {}
[5.213189] (-) TimerEvent: {}
[5.313500] (-) TimerEvent: {}
[5.413837] (-) TimerEvent: {}
[5.514293] (-) TimerEvent: {}
[5.614654] (-) TimerEvent: {}
[5.714985] (-) TimerEvent: {}
[5.815404] (-) TimerEvent: {}
[5.915675] (-) TimerEvent: {}
[6.015956] (-) TimerEvent: {}
[6.116247] (-) TimerEvent: {}
[6.216626] (-) TimerEvent: {}
[6.316895] (-) TimerEvent: {}
[6.417182] (-) TimerEvent: {}
[6.517468] (-) TimerEvent: {}
[6.617802] (-) TimerEvent: {}
[6.718078] (-) TimerEvent: {}
[6.818429] (-) TimerEvent: {}
[6.918800] (-) TimerEvent: {}
[7.019046] (-) TimerEvent: {}
[7.090113] (detected_post_processing) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/post_processing.dir/build.make:76\xef\xbc\x9aCMakeFiles/post_processing.dir/src/post_processing.cpp.o] \xe9\x94\x99\xe8\xaf\xaf 1\n'}
[7.090392] (detected_post_processing) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:139\xef\xbc\x9aCMakeFiles/post_processing.dir/all] \xe9\x94\x99\xe8\xaf\xaf 2\n'}
[7.090679] (detected_post_processing) StderrLine: {'line': b'gmake: *** [Makefile:146\xef\xbc\x9aall] \xe9\x94\x99\xe8\xaf\xaf 2\n'}
[7.092400] (detected_post_processing) CommandEnded: {'returncode': 2}
[7.099904] (detected_post_processing) JobEnded: {'identifier': 'detected_post_processing', 'rc': 2}
[7.110519] (-) EventReactorShutdown: {}
