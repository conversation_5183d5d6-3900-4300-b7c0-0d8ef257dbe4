-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found sensor_msgs: 4.8.0 (/opt/ros/humble/share/sensor_msgs/cmake)
-- Found vision_msgs: 4.1.1 (/opt/ros/humble/share/vision_msgs/cmake)
-- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)
-- Found message_filters: 4.3.7 (/opt/ros/humble/share/message_filters/cmake)
-- Found tf2: 0.25.12 (/opt/ros/humble/share/tf2/cmake)
-- Found tf2_ros: 0.25.12 (/opt/ros/humble/share/tf2_ros/cmake)
-- Found tf2_geometry_msgs: 0.25.12 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Found data_interface: 0.0.0 (/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake)
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: 
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ccag/ccag_ws/build/detected_post_processing
[35m[1mConsolidate compiler generated dependencies of target post_processing[0m
[35m[1mConsolidate compiler generated dependencies of target post_processing_test[0m
[ 25%] [32mBuilding CXX object CMakeFiles/post_processing.dir/src/post_processing.cpp.o[0m
[ 75%] Built target post_processing_test
In file included from [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:20[m[K:
[01m[K/opt/ros/humble/include/tf2_geometry_msgs/tf2_geometry_msgs/tf2_geometry_msgs.h:35:2:[m[K [01;35m[Kwarning: [m[K#warning This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wcpp-Wcpp]8;;[m[K]
   35 | #[01;35m[Kwarning[m[K This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead
      |  [01;35m[K^~~~~~~[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:[m[K In member function ‘[01m[Kstd::vector<cv::Point_<float> > DetectedPostProcessing::image_points_sort(const std::vector<cv::Vec<float, 3> >&, const Point2d&)[m[K’:
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:237:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KbottomLabel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  237 |     int [01;35m[KbottomLabel[m[K = 1 - topLabel;
      |         [01;35m[K^~~~~~~~~~~[m[K
In file included from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155[m[K,
                 from [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:1[m[K:
/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp: In instantiation of ‘[01m[Krclcpp::FutureReturnCode rclcpp::spin_until_future_complete(std::shared_ptr<_Tp>, const FutureT&, std::chrono::duration<_Rep2, _Period2>) [with NodeT = rclcpp::Client<data_interface::srv::PoseEst3d2dSrv>; FutureT = std::shared_future<std::shared_ptr<data_interface::srv::PoseEst3d2dSrv_Response_<std::allocator<void> > > >; TimeRepT = long int; TimeT = std::ratio<1, 1000>][m[K’:
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:454:45:[m[K   required from here
[01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:124:55:[m[K [01;31m[Kerror: [m[K‘[01m[Kusing element_type = class rclcpp::Client<data_interface::srv::PoseEst3d2dSrv>[m[K’ {aka ‘[01m[Kclass rclcpp::Client<data_interface::srv::PoseEst3d2dSrv>[m[K’} has no member named ‘[01m[Kget_node_base_interface[m[K’
  124 |   return rclcpp::spin_until_future_complete([01;31m[Knode_ptr->get_node_base_interface[m[K(), future, timeout);
      |                                             [01;31m[K~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~[m[K
gmake[2]: *** [CMakeFiles/post_processing.dir/build.make:76：CMakeFiles/post_processing.dir/src/post_processing.cpp.o] 错误 1
gmake[1]: *** [CMakeFiles/Makefile2:139：CMakeFiles/post_processing.dir/all] 错误 2
gmake: *** [Makefile:146：all] 错误 2
