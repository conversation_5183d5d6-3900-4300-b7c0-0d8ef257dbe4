[0.010s] Invoking command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing': CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib /usr/local/bin/cmake --build /home/<USER>/ccag/ccag_ws/build/detected_post_processing -- -j28 -l28
[0.034s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.145s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[0.172s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.174s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.180s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.189s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.201s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.233s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.235s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.317s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.361s] -- Found sensor_msgs: 4.8.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[0.384s] -- Found vision_msgs: 4.1.1 (/opt/ros/humble/share/vision_msgs/cmake)
[0.395s] -- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)
[0.411s] -- Found message_filters: 4.3.7 (/opt/ros/humble/share/message_filters/cmake)
[0.419s] -- Found tf2: 0.25.12 (/opt/ros/humble/share/tf2/cmake)
[0.424s] -- Found tf2_ros: 0.25.12 (/opt/ros/humble/share/tf2_ros/cmake)
[0.492s] -- Found tf2_geometry_msgs: 0.25.12 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
[0.497s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[0.498s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[0.499s] -- Found data_interface: 0.0.0 (/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake)
[0.513s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.575s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.575s] -- Configured cppcheck include dirs: 
[0.575s] -- Configured cppcheck exclude dirs and/or files: 
[0.575s] -- Added test 'lint_cmake' to check CMake code style
[0.576s] -- Added test 'uncrustify' to check C / C++ code style
[0.576s] -- Configured uncrustify additional arguments: 
[0.576s] -- Added test 'xmllint' to check XML markup files
[0.577s] -- Configuring done
[0.592s] -- Generating done
[0.597s] -- Build files have been written to: /home/<USER>/ccag/ccag_ws/build/detected_post_processing
[0.620s] [35m[1mConsolidate compiler generated dependencies of target post_processing[0m
[0.621s] [35m[1mConsolidate compiler generated dependencies of target post_processing_test[0m
[0.640s] [ 25%] [32mBuilding CXX object CMakeFiles/post_processing.dir/src/post_processing.cpp.o[0m
[0.641s] [ 75%] Built target post_processing_test
[0.868s] In file included from [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:20[m[K:
[0.868s] [01m[K/opt/ros/humble/include/tf2_geometry_msgs/tf2_geometry_msgs/tf2_geometry_msgs.h:35:2:[m[K [01;35m[Kwarning: [m[K#warning This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wcpp-Wcpp]8;;[m[K]
[0.868s]    35 | #[01;35m[Kwarning[m[K This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead
[0.868s]       |  [01;35m[K^~~~~~~[m[K
[3.410s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:[m[K In member function ‘[01m[Kstd::vector<cv::Point_<float> > DetectedPostProcessing::image_points_sort(const std::vector<cv::Vec<float, 3> >&, const Point2d&)[m[K’:
[3.410s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:237:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KbottomLabel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[3.410s]   237 |     int [01;35m[KbottomLabel[m[K = 1 - topLabel;
[3.410s]       |         [01;35m[K^~~~~~~~~~~[m[K
[3.579s] In file included from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155[m[K,
[3.579s]                  from [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:1[m[K:
[3.579s] /opt/ros/humble/include/rclcpp/rclcpp/executors.hpp: In instantiation of ‘[01m[Krclcpp::FutureReturnCode rclcpp::spin_until_future_complete(std::shared_ptr<_Tp>, const FutureT&, std::chrono::duration<_Rep2, _Period2>) [with NodeT = rclcpp::Client<data_interface::srv::PoseEst3d2dSrv>; FutureT = std::shared_future<std::shared_ptr<data_interface::srv::PoseEst3d2dSrv_Response_<std::allocator<void> > > >; TimeRepT = long int; TimeT = std::ratio<1, 1000>][m[K’:
[3.579s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:454:45:[m[K   required from here
[3.580s] [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:124:55:[m[K [01;31m[Kerror: [m[K‘[01m[Kusing element_type = class rclcpp::Client<data_interface::srv::PoseEst3d2dSrv>[m[K’ {aka ‘[01m[Kclass rclcpp::Client<data_interface::srv::PoseEst3d2dSrv>[m[K’} has no member named ‘[01m[Kget_node_base_interface[m[K’
[3.580s]   124 |   return rclcpp::spin_until_future_complete([01;31m[Knode_ptr->get_node_base_interface[m[K(), future, timeout);
[3.580s]       |                                             [01;31m[K~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~[m[K
[7.090s] gmake[2]: *** [CMakeFiles/post_processing.dir/build.make:76：CMakeFiles/post_processing.dir/src/post_processing.cpp.o] 错误 1
[7.090s] gmake[1]: *** [CMakeFiles/Makefile2:139：CMakeFiles/post_processing.dir/all] 错误 2
[7.090s] gmake: *** [Makefile:146：all] 错误 2
[7.092s] Invoked command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing' returned '2': CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib /usr/local/bin/cmake --build /home/<USER>/ccag/ccag_ws/build/detected_post_processing -- -j28 -l28
