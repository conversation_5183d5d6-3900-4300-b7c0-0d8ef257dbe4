[ 25%] [32mBuilding CXX object CMakeFiles/post_processing.dir/src/post_processing.cpp.o[0m
[ 75%] Built target post_processing_test
In file included from [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:20[m[K:
[01m[K/opt/ros/humble/include/tf2_geometry_msgs/tf2_geometry_msgs/tf2_geometry_msgs.h:35:2:[m[K [01;35m[Kwarning: [m[K#warning This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wcpp-Wcpp]8;;[m[K]
   35 | #[01;35m[Kwarning[m[K This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead
      |  [01;35m[K^~~~~~~[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:620:19:[m[K [01;31m[Kerror: [m[K‘[01m[Kstd_srvs[m[K’ was not declared in this scope
  620 |   rclcpp::Service<[01;31m[Kstd_srvs[m[K::srv::Empty>::SharedPtr reset_service_;
      |                   [01;31m[K^~~~~~~~[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:620:39:[m[K [01;31m[Kerror: [m[Ktemplate argument 1 is invalid
  620 |   rclcpp::Service<std_srvs::srv::Empty[01;31m[K>[m[K::SharedPtr reset_service_;
      |                                       [01;31m[K^[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:620:42:[m[K [01;31m[Kerror: [m[Kexpected ‘[01m[K;[m[K’ at end of member declaration
  620 |   rclcpp::Service<std_srvs::srv::Empty>::[01;31m[KSharedPtr[m[K reset_service_;
      |                                          [01;31m[K^~~~~~~~~[m[K
      |                                                   [32m[K;[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:620:52:[m[K [01;31m[Kerror: [m[K‘[01m[Kreset_service_[m[K’ does not name a type
  620 |   rclcpp::Service<std_srvs::srv::Empty>::SharedPtr [01;31m[Kreset_service_[m[K;
      |                                                    [01;31m[K^~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:621:19:[m[K [01;31m[Kerror: [m[K‘[01m[Kstd_srvs[m[K’ was not declared in this scope
  621 |   rclcpp::Service<[01;31m[Kstd_srvs[m[K::srv::Trigger>::SharedPtr status_service_;
      |                   [01;31m[K^~~~~~~~[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:621:41:[m[K [01;31m[Kerror: [m[Ktemplate argument 1 is invalid
  621 |   rclcpp::Service<std_srvs::srv::Trigger[01;31m[K>[m[K::SharedPtr status_service_;
      |                                         [01;31m[K^[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:621:44:[m[K [01;31m[Kerror: [m[Kexpected ‘[01m[K;[m[K’ at end of member declaration
  621 |   rclcpp::Service<std_srvs::srv::Trigger>::[01;31m[KSharedPtr[m[K status_service_;
      |                                            [01;31m[K^~~~~~~~~[m[K
      |                                                     [32m[K;[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:621:44:[m[K [01;31m[Kerror: [m[Kredeclaration of ‘[01m[Kint DetectedPostProcessing::SharedPtr[m[K’
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:620:42:[m[K [01;36m[Knote: [m[Kprevious declaration ‘[01m[Kint DetectedPostProcessing::SharedPtr[m[K’
  620 |   rclcpp::Service<std_srvs::srv::Empty>::[01;36m[KSharedPtr[m[K reset_service_;
      |                                          [01;36m[K^~~~~~~~~[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:621:54:[m[K [01;31m[Kerror: [m[K‘[01m[Kstatus_service_[m[K’ does not name a type
  621 |   rclcpp::Service<std_srvs::srv::Trigger>::SharedPtr [01;31m[Kstatus_service_[m[K;
      |                                                      [01;31m[K^~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:[m[K In member function ‘[01m[Kstd::vector<cv::Point_<float> > DetectedPostProcessing::image_points_sort(const std::vector<cv::Vec<float, 3> >&, const Point2d&)[m[K’:
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:358:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KbottomLabel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  358 |     int [01;35m[KbottomLabel[m[K = 1 - topLabel;
      |         [01;35m[K^~~~~~~~~~~[m[K
gmake[2]: *** [CMakeFiles/post_processing.dir/build.make:76：CMakeFiles/post_processing.dir/src/post_processing.cpp.o] 错误 1
gmake[1]: *** [CMakeFiles/Makefile2:139：CMakeFiles/post_processing.dir/all] 错误 2
gmake: *** [Makefile:146：all] 错误 2
