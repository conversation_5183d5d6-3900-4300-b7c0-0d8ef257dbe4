[0.011s] Invoking command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing': CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib /usr/local/bin/cmake --build /home/<USER>/ccag/ccag_ws/build/detected_post_processing -- -j28 -l28
[0.061s] [ 25%] [32mBuilding CXX object CMakeFiles/post_processing.dir/src/post_processing.cpp.o[0m
[0.062s] [ 75%] Built target post_processing_test
[0.292s] In file included from [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:20[m[K:
[0.292s] [01m[K/opt/ros/humble/include/tf2_geometry_msgs/tf2_geometry_msgs/tf2_geometry_msgs.h:35:2:[m[K [01;35m[Kwarning: [m[K#warning This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wcpp-Wcpp]8;;[m[K]
[0.292s]    35 | #[01;35m[Kwarning[m[K This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead
[0.292s]       |  [01;35m[K^~~~~~~[m[K
[2.456s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:620:19:[m[K [01;31m[Kerror: [m[K‘[01m[Kstd_srvs[m[K’ was not declared in this scope
[2.456s]   620 |   rclcpp::Service<[01;31m[Kstd_srvs[m[K::srv::Empty>::SharedPtr reset_service_;
[2.456s]       |                   [01;31m[K^~~~~~~~[m[K
[2.456s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:620:39:[m[K [01;31m[Kerror: [m[Ktemplate argument 1 is invalid
[2.456s]   620 |   rclcpp::Service<std_srvs::srv::Empty[01;31m[K>[m[K::SharedPtr reset_service_;
[2.456s]       |                                       [01;31m[K^[m[K
[2.456s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:620:42:[m[K [01;31m[Kerror: [m[Kexpected ‘[01m[K;[m[K’ at end of member declaration
[2.456s]   620 |   rclcpp::Service<std_srvs::srv::Empty>::[01;31m[KSharedPtr[m[K reset_service_;
[2.456s]       |                                          [01;31m[K^~~~~~~~~[m[K
[2.456s]       |                                                   [32m[K;[m[K
[2.458s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:620:52:[m[K [01;31m[Kerror: [m[K‘[01m[Kreset_service_[m[K’ does not name a type
[2.458s]   620 |   rclcpp::Service<std_srvs::srv::Empty>::SharedPtr [01;31m[Kreset_service_[m[K;
[2.458s]       |                                                    [01;31m[K^~~~~~~~~~~~~~[m[K
[2.464s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:621:19:[m[K [01;31m[Kerror: [m[K‘[01m[Kstd_srvs[m[K’ was not declared in this scope
[2.465s]   621 |   rclcpp::Service<[01;31m[Kstd_srvs[m[K::srv::Trigger>::SharedPtr status_service_;
[2.465s]       |                   [01;31m[K^~~~~~~~[m[K
[2.465s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:621:41:[m[K [01;31m[Kerror: [m[Ktemplate argument 1 is invalid
[2.465s]   621 |   rclcpp::Service<std_srvs::srv::Trigger[01;31m[K>[m[K::SharedPtr status_service_;
[2.465s]       |                                         [01;31m[K^[m[K
[2.465s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:621:44:[m[K [01;31m[Kerror: [m[Kexpected ‘[01m[K;[m[K’ at end of member declaration
[2.465s]   621 |   rclcpp::Service<std_srvs::srv::Trigger>::[01;31m[KSharedPtr[m[K status_service_;
[2.465s]       |                                            [01;31m[K^~~~~~~~~[m[K
[2.465s]       |                                                     [32m[K;[m[K
[2.465s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:621:44:[m[K [01;31m[Kerror: [m[Kredeclaration of ‘[01m[Kint DetectedPostProcessing::SharedPtr[m[K’
[2.465s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:620:42:[m[K [01;36m[Knote: [m[Kprevious declaration ‘[01m[Kint DetectedPostProcessing::SharedPtr[m[K’
[2.465s]   620 |   rclcpp::Service<std_srvs::srv::Empty>::[01;36m[KSharedPtr[m[K reset_service_;
[2.465s]       |                                          [01;36m[K^~~~~~~~~[m[K
[2.467s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:621:54:[m[K [01;31m[Kerror: [m[K‘[01m[Kstatus_service_[m[K’ does not name a type
[2.467s]   621 |   rclcpp::Service<std_srvs::srv::Trigger>::SharedPtr [01;31m[Kstatus_service_[m[K;
[2.467s]       |                                                      [01;31m[K^~~~~~~~~~~~~~~[m[K
[2.974s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:[m[K In member function ‘[01m[Kstd::vector<cv::Point_<float> > DetectedPostProcessing::image_points_sort(const std::vector<cv::Vec<float, 3> >&, const Point2d&)[m[K’:
[2.974s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:358:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KbottomLabel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[2.974s]   358 |     int [01;35m[KbottomLabel[m[K = 1 - topLabel;
[2.974s]       |         [01;35m[K^~~~~~~~~~~[m[K
[6.654s] gmake[2]: *** [CMakeFiles/post_processing.dir/build.make:76：CMakeFiles/post_processing.dir/src/post_processing.cpp.o] 错误 1
[6.654s] gmake[1]: *** [CMakeFiles/Makefile2:139：CMakeFiles/post_processing.dir/all] 错误 2
[6.654s] gmake: *** [Makefile:146：all] 错误 2
[6.656s] Invoked command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing' returned '2': CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib /usr/local/bin/cmake --build /home/<USER>/ccag/ccag_ws/build/detected_post_processing -- -j28 -l28
